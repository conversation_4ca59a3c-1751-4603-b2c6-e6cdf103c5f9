package com.rs.framework.web.core.handler;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.rs.framework.common.exception.ServerException;
import com.rs.framework.common.exception.ServiceException;
import com.rs.framework.common.exception.util.ServiceExceptionUtil;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.collection.SetUtils;
import com.rs.framework.common.util.json.JsonUtils;
import com.rs.framework.common.util.monitor.TracerUtils;
import com.rs.framework.common.util.servlet.ServletUtils;
import com.rs.framework.web.core.util.WebFrameworkUtils;
import com.rs.module.infra.api.logger.dto.ApiErrorLogCreateReqDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.util.Assert;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.rs.framework.common.exception.enums.GlobalErrorCodeConstants.*;

/**
 * 全局异常处理器，将 Exception 翻译成 CommonResult + 对应的异常编号
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@AllArgsConstructor
@Slf4j
public class GlobalExceptionHandlerX {

    /**
     * 忽略的 ServiceException 错误提示，避免打印过多 logger
     */
    public static final Set<String> IGNORE_ERROR_MESSAGES = SetUtils.asSet("无效的刷新令牌", "请勿重复提交");


    /**
     * 处理所有异常，主要是提供给 Filter 使用
     * 因为 Filter 不走 SpringMVC 的流程，但是我们又需要兜底处理异常，所以这里提供一个全量的异常处理过程，保持逻辑统一。
     *
     * @param request 请求
     * @param ex      异常
     * @return 通用返回
     */
    public CommonResult<?> allExceptionHandler(HttpServletRequest request, Throwable ex) {

        if (ex instanceof MissingServletRequestParameterException) {
            return missingServletRequestParameterExceptionHandler((MissingServletRequestParameterException) ex);
        }
        if (ex instanceof MethodArgumentTypeMismatchException) {
            return methodArgumentTypeMismatchExceptionHandler((MethodArgumentTypeMismatchException) ex);
        }
        if (ex instanceof MethodArgumentNotValidException) {
            return methodArgumentNotValidExceptionExceptionHandler((MethodArgumentNotValidException) ex);
        }
        if (ex instanceof BindException) {
            return bindExceptionHandler((BindException) ex);
        }
        if (ex instanceof ConstraintViolationException) {
            return constraintViolationExceptionHandler((ConstraintViolationException) ex);
        }
        if (ex instanceof ValidationException) {
            return validationException((ValidationException) ex);
        }
        if (ex instanceof NoHandlerFoundException) {
            return noHandlerFoundExceptionHandler((NoHandlerFoundException) ex);
        }
//        if (ex instanceof NoResourceFoundException) {
//            return noResourceFoundExceptionHandler(request, (NoResourceFoundException) ex);
//        }
        if (ex instanceof HttpRequestMethodNotSupportedException) {
            return httpRequestMethodNotSupportedExceptionHandler((HttpRequestMethodNotSupportedException) ex);
        }
        if (ex instanceof ServiceException) {
            return serviceExceptionHandler((ServiceException) ex);
        }
        if (ex instanceof AccessDeniedException) {
            return accessDeniedExceptionHandler(request, (AccessDeniedException) ex);
        }
        // 处理数据库异常
        if (isDatabaseException(ex)) {
            return databaseExceptionHandler(request, ex);
        }
        return defaultExceptionHandler(request, ex);
    }

    /**
     * 处理 SpringMVC 请求参数缺失
     * <p>
     * 例如说，接口上设置了 @RequestParam("xx") 参数，结果并未传递 xx 参数
     */
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public CommonResult<?> missingServletRequestParameterExceptionHandler(MissingServletRequestParameterException ex) {
        log.warn("[missingServletRequestParameterExceptionHandler]", ex);
        return CommonResult.error(BAD_REQUEST.getCode(), String.format("请求参数缺失:%s", ex.getParameterName()));
    }

    /**
     * 处理 SpringMVC 请求参数类型错误
     * <p>
     * 例如说，接口上设置了 @RequestParam("xx") 参数为 Integer，结果传递 xx 参数类型为 String
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public CommonResult<?> methodArgumentTypeMismatchExceptionHandler(MethodArgumentTypeMismatchException ex) {
        log.warn("[methodArgumentTypeMismatchExceptionHandler]", ex);
        return CommonResult.error(BAD_REQUEST.getCode(), String.format("请求参数类型错误:%s", ex.getMessage()));
    }

    /**
     * 处理 SpringMVC 参数校验不正确
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResult<?> methodArgumentNotValidExceptionExceptionHandler(MethodArgumentNotValidException ex) {
        log.warn("[methodArgumentNotValidExceptionExceptionHandler]", ex);
        FieldError fieldError = ex.getBindingResult().getFieldError();
        assert fieldError != null; // 断言，避免告警
        return CommonResult.error(BAD_REQUEST.getCode(), String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
    }

    /**
     * 处理 SpringMVC 参数绑定不正确，本质上也是通过 Validator 校验
     */
    @ExceptionHandler(BindException.class)
    public CommonResult<?> bindExceptionHandler(BindException ex) {
        log.warn("[handleBindException]", ex);
        FieldError fieldError = ex.getFieldError();
        assert fieldError != null; // 断言，避免告警
        return CommonResult.error(BAD_REQUEST.getCode(), String.format("请求参数不正确:%s", fieldError.getDefaultMessage()));
    }

    /**
     * 处理 SpringMVC 请求参数类型错误
     * <p>
     * 例如说，接口上设置了 @RequestBody实体中 xx 属性类型为 Integer，结果传递 xx 参数类型为 String
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public CommonResult<?> methodArgumentTypeInvalidFormatExceptionHandler(HttpMessageNotReadableException ex) {
        log.warn("[methodArgumentTypeInvalidFormatExceptionHandler]", ex);
        if (ex.getCause() instanceof InvalidFormatException) {
            InvalidFormatException invalidFormatException = (InvalidFormatException) ex.getCause();
            return CommonResult.error(BAD_REQUEST.getCode(), String.format("请求参数类型错误:%s", invalidFormatException.getValue()));
        } else {
            return defaultExceptionHandler(ServletUtils.getRequest(), ex);
        }
    }

    /**
     * 处理 Validator 校验不通过产生的异常
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public CommonResult<?> constraintViolationExceptionHandler(ConstraintViolationException ex) {
        log.warn("[constraintViolationExceptionHandler]", ex);
        ConstraintViolation<?> constraintViolation = ex.getConstraintViolations().iterator().next();
        return CommonResult.error(BAD_REQUEST.getCode(), String.format("请求参数不正确:%s", constraintViolation.getMessage()));
    }

    /**
     * 处理 Dubbo Consumer 本地参数校验时，抛出的 ValidationException 异常
     */
    @ExceptionHandler(value = ValidationException.class)
    public CommonResult<?> validationException(ValidationException ex) {
        log.warn("[constraintViolationExceptionHandler]", ex);
        // 无法拼接明细的错误信息，因为 Dubbo Consumer 抛出 ValidationException 异常时，是直接的字符串信息，且人类不可读
        return CommonResult.error(BAD_REQUEST);
    }

    /**
     * 处理 SpringMVC 请求地址不存在
     * <p>
     * 注意，它需要设置如下两个配置项：
     * 1. spring.mvc.throw-exception-if-no-handler-found 为 true
     * 2. spring.mvc.static-path-pattern 为 /statics/**
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    public CommonResult<?> noHandlerFoundExceptionHandler(NoHandlerFoundException ex) {
        log.warn("[noHandlerFoundExceptionHandler]", ex);
        return CommonResult.error(NOT_FOUND.getCode(), String.format("请求地址不存在:%s", ex.getRequestURL()));
    }

//    /**
//     * 处理 SpringMVC 请求地址不存在
//     */
//    @ExceptionHandler(NoResourceFoundException.class)
//    private CommonResult<?> noResourceFoundExceptionHandler(HttpServletRequest req, NoResourceFoundException ex) {
//        log.warn("[noResourceFoundExceptionHandler]", ex);
//        return CommonResult.error(NOT_FOUND.getCode(), String.format("请求地址不存在:%s", ex.getResourcePath()));
//    }

    /**
     * 处理 SpringMVC 请求方法不正确
     * <p>
     * 例如说，A 接口的方法为 GET 方式，结果请求方法为 POST 方式，导致不匹配
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public CommonResult<?> httpRequestMethodNotSupportedExceptionHandler(HttpRequestMethodNotSupportedException ex) {
        log.warn("[httpRequestMethodNotSupportedExceptionHandler]", ex);
        return CommonResult.error(METHOD_NOT_ALLOWED.getCode(), String.format("请求方法不正确:%s", ex.getMessage()));
    }

    /**
     * 处理 Spring Security 权限不足的异常
     * <p>
     * 来源是，使用 @PreAuthorize 注解，AOP 进行权限拦截
     */
    @ExceptionHandler(value = AccessDeniedException.class)
    public CommonResult<?> accessDeniedExceptionHandler(HttpServletRequest req, AccessDeniedException ex) {
        log.warn("[accessDeniedExceptionHandler][userId({}) 无法访问 url({})]", WebFrameworkUtils.getLoginUserId(req),
                req.getRequestURL(), ex);
        return CommonResult.error(FORBIDDEN);
    }

    /**
     * 处理 SpringMVC 请求参数类型错误
     * <p>
     * 例如说，接口上设置了 @RequestBody实体中 xx 属性类型为 Integer，结果传递 xx 参数类型为 String
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public CommonResult<?> illegalArgumentExceptionHandler(IllegalArgumentException ex) {
        log.error("[illegalArgumentExceptionHandler]", ex);
        return CommonResult.error(BAD_REQUEST.getCode(), String.format("请求参数错误:%s", ex.getMessage()));
    }

    /**
     * 处理业务异常 ServiceException
     * <p>
     * 例如说，商品库存不足，用户手机号已存在。
     */
    @ExceptionHandler(value = ServiceException.class)
    public CommonResult<?> serviceExceptionHandler(ServiceException ex) {
        // 不包含的时候，才进行打印，避免 ex 堆栈过多
        if (!IGNORE_ERROR_MESSAGES.contains(ex.getMessage())) {
            // 即使打印，也只打印第一层 StackTraceElement，并且使用 warn 在控制台输出，更容易看到
            try {
                StackTraceElement[] stackTraces = ex.getStackTrace();
                for (StackTraceElement stackTrace : stackTraces) {
                    if (ObjUtil.notEqual(stackTrace.getClassName(), ServiceExceptionUtil.class.getName())) {
                        log.warn("[serviceExceptionHandler]\n\t{}", stackTrace);
                        break;
                    }
                }
            } catch (Exception ignored) {
                // 忽略日志，避免影响主流程
            }
        }
        return CommonResult.error(ex.getCode(), ex.getMessage());
    }

    /**
     * 处理业务异常 ServerException
     * <p>
     * 例如说，商品库存不足，用户手机号已存在。
     */
    @ExceptionHandler(value = ServerException.class)
    public CommonResult<?> serverExceptionHandler(ServerException ex) {
        // 不包含的时候，才进行打印，避免 ex 堆栈过多
        if (!IGNORE_ERROR_MESSAGES.contains(ex.getMessage())) {
            // 即使打印，也只打印第一层 StackTraceElement，并且使用 warn 在控制台输出，更容易看到
            try {
                StackTraceElement[] stackTraces = ex.getStackTrace();
                for (StackTraceElement stackTrace : stackTraces) {
                    if (ObjUtil.notEqual(stackTrace.getClassName(), ServiceExceptionUtil.class.getName())) {
                        log.warn("[serverExceptionHandler]\n\t{}", stackTrace);
                        break;
                    }
                }
            } catch (Exception ignored) {
                // 忽略日志，避免影响主流程
            }
        }
        return CommonResult.error(ex.getCode(), ex.getMessage());
    }

    /**
     * 处理数据库异常，转换为用户友好的提示信息
     */
    public CommonResult<?> databaseExceptionHandler(HttpServletRequest req, Throwable ex) {
        log.error("[databaseExceptionHandler] 数据库异常", ex);

        // 插入异常日志
        createExceptionLog(req, ex);

        // 获取用户友好的错误信息
        String userFriendlyMessage = convertDatabaseExceptionToUserMessage(ex);

        // 创建详细异常信息
        CommonResult.ExceptionDetail exceptionDetail = buildExceptionDetail(req, ex);

        return CommonResult.error(BAD_REQUEST.getCode(), userFriendlyMessage, exceptionDetail);
    }

    /**
     * 处理系统异常，兜底处理所有的一切
     */
    @ExceptionHandler(value = Exception.class)
    public CommonResult<?> defaultExceptionHandler(HttpServletRequest req, Throwable ex) {
        if (isDatabaseException(ex)) {
            return databaseExceptionHandler(req, ex);
        }
        // 情况一：处理表不存在的异常
        CommonResult<?> tableNotExistsResult = handleTableNotExists(ex);
        if (tableNotExistsResult != null) {
            return tableNotExistsResult;
        }

        // 情况二：处理异常
        // 打印详细的异常信息，包括异常类和位置
        printDetailedExceptionInfo(ex);

        // 插入异常日志
        createExceptionLog(req, ex);

        // 创建详细异常信息
        CommonResult.ExceptionDetail exceptionDetail = buildExceptionDetail(req, ex);

        // 返回 ERROR CommonResult
        return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "系统内部错误，请联系管理员", exceptionDetail);
    }

    /**
     * 打印详细的异常信息，包括异常类和具体位置
     */
    private void printDetailedExceptionInfo(Throwable ex) {
        log.error("[defaultExceptionHandler] 异常详细信息:");
        log.error("异常类型: {}", ex.getClass().getName());
        log.error("异常消息: {}", ex.getMessage());

        // 打印异常堆栈的前几层，显示具体的出错位置
        StackTraceElement[] stackTrace = ex.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            log.error("异常发生位置:");
            // 打印前5层堆栈信息，通常足够定位问题
            int maxPrint = Math.min(5, stackTrace.length);
            for (int i = 0; i < maxPrint; i++) {
                StackTraceElement element = stackTrace[i];
                log.error("  [{}] {}::{} ({}:{})",
                        i + 1,
                        element.getClassName(),
                        element.getMethodName(),
                        element.getFileName(),
                        element.getLineNumber());
            }
        }

        // 如果有原因异常，也打印出来
        Throwable cause = ex.getCause();
        if (cause != null && cause != ex) {
            log.error("根本原因异常:");
            log.error("  异常类型: {}", cause.getClass().getName());
            log.error("  异常消息: {}", cause.getMessage());

            StackTraceElement[] causeStackTrace = cause.getStackTrace();
            if (causeStackTrace != null && causeStackTrace.length > 0) {
                log.error("  根本原因位置:");
                // 只打印根本原因的第一层位置
                StackTraceElement causeElement = causeStackTrace[0];
                log.error("    {}::{} ({}:{})",
                        causeElement.getClassName(),
                        causeElement.getMethodName(),
                        causeElement.getFileName(),
                        causeElement.getLineNumber());
            }
        }

        // 完整的异常堆栈（用于调试）
        log.error("[defaultExceptionHandler] 完整异常堆栈:", ex);
    }

    /**
     * 判断是否为数据库异常
     */
    private boolean isDatabaseException(Throwable ex) {
        if (ex == null) {
            return false;
        }

        // 检查异常类型
        String exceptionName = ex.getClass().getName();
        if (exceptionName.contains("SQLException") ||
                exceptionName.contains("DataIntegrityViolationException") ||
                exceptionName.contains("PSQLException") ||
                exceptionName.contains("MySQLIntegrityConstraintViolationException") ||
                exceptionName.contains("ConstraintViolationException") ||
                exceptionName.contains("DataAccessException")) {
            return true;
        }

        // 检查异常消息
        String message = ExceptionUtil.getRootCauseMessage(ex);
        if (StrUtil.isNotBlank(message)) {
            return message.contains("违反了非空约束") ||
                    message.contains("duplicate key") ||
                    message.contains("foreign key constraint") ||
                    message.contains("unique constraint") ||
                    message.contains("not-null constraint") ||
                    message.contains("check constraint") ||
                    message.contains("数据过长") ||
                    message.contains("Data too long") ||
                    message.contains("Error updating database") ||
                    message.contains("PSQLException");
        }

        // 递归检查原因异常
        return isDatabaseException(ex.getCause());
    }

    /**
     * 将数据库异常转换为用户友好的提示信息
     */
    private String convertDatabaseExceptionToUserMessage(Throwable ex) {
        String message = ExceptionUtil.getRootCauseMessage(ex);

        if (StrUtil.isBlank(message)) {
            return "数据操作失败，请检查输入数据";
        }

        // 提取表名和字段名
        String tableName = extractTableNameFromMessage(message);
        String friendlyTableName = convertTableNameToFriendly(tableName);

        // PostgreSQL 非空约束违反
        if (message.contains("违反了非空约束")) {
            String fieldName = extractFieldNameFromConstraintMessage(message);
            if (StrUtil.isNotBlank(fieldName)) {
                String friendlyFieldName = convertFieldNameToFriendly(fieldName);
                if (StrUtil.isNotBlank(friendlyTableName)) {
                    return String.format("在 [%s] 中，必填字段 [%s] 不能为空，请填写完整信息", friendlyTableName, friendlyFieldName);
                } else {
                    return String.format("必填字段 [%s] 不能为空，请填写完整信息", friendlyFieldName);
                }
            }
            if (StrUtil.isNotBlank(friendlyTableName)) {
                return String.format("在 [%s] 中存在必填字段未填写，请检查并填写完整信息", friendlyTableName);
            }
            return "存在必填字段未填写，请检查并填写完整信息";
        }

        // 唯一约束违反
        if (message.contains("duplicate key") || message.contains("unique constraint")) {
            if (StrUtil.isNotBlank(friendlyTableName)) {
                return String.format("在 [%s] 中数据已存在，不能重复添加", friendlyTableName);
            }
            return "数据已存在，不能重复添加";
        }

        // 外键约束违反
        if (message.contains("foreign key constraint")) {
            if (StrUtil.isNotBlank(friendlyTableName)) {
                return String.format("在 [%s] 中关联数据不存在或已被删除，请刷新页面后重试", friendlyTableName);
            }
            return "关联数据不存在或已被删除，请刷新页面后重试";
        }

        // 数据过长
        if (message.contains("数据过长") || message.contains("Data too long")) {
            if (StrUtil.isNotBlank(friendlyTableName)) {
                return String.format("在 [%s] 中输入的数据过长，请缩短内容长度", friendlyTableName);
            }
            return "输入的数据过长，请缩短内容长度";
        }

        // 检查约束违反
        if (message.contains("check constraint")) {
            if (StrUtil.isNotBlank(friendlyTableName)) {
                return String.format("在 [%s] 中数据格式不正确，请检查输入内容", friendlyTableName);
            }
            return "数据格式不正确，请检查输入内容";
        }

        // 默认数据库错误提示
        if (StrUtil.isNotBlank(friendlyTableName)) {
            return String.format("在 [%s] 中数据操作失败，请检查输入数据是否正确", friendlyTableName);
        }
        return "数据操作失败，请检查输入数据是否正确";
    }

    /**
     * 从错误消息中提取表名
     */
    private String extractTableNameFromMessage(String message) {
        try {
            // 从SQL语句中提取表名: INSERT INTO acp_gj_in_out_records
            Pattern pattern = Pattern.compile("INSERT\\s+INTO\\s+([a-zA-Z_][a-zA-Z0-9_]*)", Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(message);
            if (matcher.find()) {
                return matcher.group(1);
            }

            // 从SQL语句中提取表名: UPDATE table_name
            pattern = Pattern.compile("UPDATE\\s+([a-zA-Z_][a-zA-Z0-9_]*)", Pattern.CASE_INSENSITIVE);
            matcher = pattern.matcher(message);
            if (matcher.find()) {
                return matcher.group(1);
            }

            // 从SQL语句中提取表名: DELETE FROM table_name
            pattern = Pattern.compile("DELETE\\s+FROM\\s+([a-zA-Z_][a-zA-Z0-9_]*)", Pattern.CASE_INSENSITIVE);
            matcher = pattern.matcher(message);
            if (matcher.find()) {
                return matcher.group(1);
            }

            // 从约束名中提取表名 (通常约束名包含表名)
            pattern = Pattern.compile("constraint\\s*[\"']([a-zA-Z_][a-zA-Z0-9_]*)", Pattern.CASE_INSENSITIVE);
            matcher = pattern.matcher(message);
            if (matcher.find()) {
                String constraintName = matcher.group(1);
                // 如果约束名包含表名前缀，提取表名
                if (constraintName.contains("_")) {
                    String[] parts = constraintName.split("_");
                    if (parts.length >= 2) {
                        return parts[0] + "_" + parts[1]; // 通常表名是前两部分
                    }
                }
            }

        } catch (Exception e) {
            log.warn("提取表名失败", e);
        }
        return null;
    }

    /**
     * 从约束违反消息中提取字段名
     */
    private String extractFieldNameFromConstraintMessage(String message) {
        try {
            // PostgreSQL: 错误: 在字段 "room_id" 中空值违反了非空约束
            Pattern pattern = Pattern.compile("在字段\\s*[\"']([^\"']+)[\"']\\s*中");
            Matcher matcher = pattern.matcher(message);
            if (matcher.find()) {
                return matcher.group(1);
            }

            // 其他格式的字段名提取
            pattern = Pattern.compile("column\\s*[\"']([^\"']+)[\"']");
            matcher = pattern.matcher(message);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.warn("提取字段名失败", e);
        }
        return null;
    }

    /**
     * 将数据库表名转换为用户友好的名称
     */
    private String convertTableNameToFriendly(String tableName) {
        if (StrUtil.isBlank(tableName)) {
            return null;
        }

        // 常见表名映射
        Map<String, String> tableMapping = MapUtil.<String, String>builder()
                // 监管相关表
                .put("acp_gj_in_out_records", "进出记录")
                .put("acp_gj_prisoner", "在押人员")
                .put("acp_gj_room", "监室信息")
                .put("acp_gj_police", "民警信息")
                .put("acp_gj_visit", "会见记录")
                .put("acp_gj_medical", "医疗记录")
                .put("acp_gj_education", "教育记录")
                .put("acp_gj_work", "劳动记录")
                .put("acp_gj_discipline", "纪律记录")
                .put("acp_gj_property", "财物管理")

                // 基础数据表
                .put("sys_user", "用户信息")
                .put("sys_role", "角色信息")
                .put("sys_menu", "菜单信息")
                .put("sys_dept", "部门信息")
                .put("sys_dict_data", "字典数据")
                .put("sys_dict_type", "字典类型")
                .put("sys_config", "系统配置")
                .put("sys_notice", "通知公告")
                .put("sys_log", "系统日志")

                // 业务表
                .put("biz_order", "订单信息")
                .put("biz_product", "产品信息")
                .put("biz_customer", "客户信息")
                .put("biz_contract", "合同信息")

                // 其他常见表
                .put("base_organization", "机构信息")
                .put("base_person", "人员信息")
                .put("base_room", "房间信息")
                .put("base_device", "设备信息")
                .build();

        String friendlyName = tableMapping.get(tableName.toLowerCase());
        if (StrUtil.isNotBlank(friendlyName)) {
            return friendlyName;
        }

        // 如果没有找到映射，尝试根据表名前缀推断
        String lowerTableName = tableName.toLowerCase();
        if (lowerTableName.startsWith("acp_gj_")) {
            return "监管业务";
        } else if (lowerTableName.startsWith("sys_")) {
            return "系统管理";
        } else if (lowerTableName.startsWith("base_")) {
            return "基础数据";
        } else if (lowerTableName.startsWith("biz_")) {
            return "业务数据";
        }

        return null; // 无法识别的表名返回null，不显示表名信息
    }

    /**
     * 将数据库字段名转换为用户友好的名称
     */
    private String convertFieldNameToFriendly(String fieldName) {
        if (StrUtil.isBlank(fieldName)) {
            return fieldName;
        }

        // 常见字段名映射
        Map<String, String> fieldMapping = MapUtil.<String, String>builder()
                .put("room_id", "监室编号")
                .put("prisoner_id", "人员")
                .put("user_id", "用户")
                .put("org_code", "机构")
                .put("create_time", "创建时间")
                .put("update_time", "更新时间")
                .put("jgrybm", "人员编码")
                .put("jgryxm", "人员姓名")
                .put("business_type", "业务类型")
                .put("business_id", "业务编号")
                .put("supervisor_police", "监管民警")
                .put("supervisor_police_sfzh", "监管民警身份证")
                .put("inout_type", "进出类型")
                .put("status", "状态")
                .put("add_user", "创建人")
                .put("add_user_name", "创建人姓名")
                .put("add_time", "创建时间")
                .put("update_user", "更新人")
                .put("update_user_name", "更新人姓名")
                .put("update_time", "更新时间")
                .put("city_name", "城市名称")
                .put("city_code", "城市代码")
                .put("reg_name", "区域名称")
                .put("reg_code", "区域代码")
                .put("org_name", "机构名称")
                .put("org_code", "机构代码")
                .put("is_del", "删除标识")
                .put("data_sources", "数据来源")
                .put("business_sub_type", "业务子类型")
                .build();

        return fieldMapping.getOrDefault(fieldName.toLowerCase(), fieldName);
    }

    private void createExceptionLog(HttpServletRequest req, Throwable e) {
        // 插入错误日志
        ApiErrorLogCreateReqDTO errorLog = new ApiErrorLogCreateReqDTO();
        try {
            // 初始化 errorLog
            buildExceptionLog(errorLog, req, e);
            // 执行插入 errorLog
//            apiErrorLogApi.createApiErrorLogAsync(errorLog);
        } catch (Throwable th) {
            log.error("[createExceptionLog][url({}) log({}) 发生异常]", req.getRequestURI(), JsonUtils.toJsonString(errorLog), th);
        }
    }

    private void buildExceptionLog(ApiErrorLogCreateReqDTO errorLog, HttpServletRequest request, Throwable e) {
        // 处理用户信息
        errorLog.setUserId(WebFrameworkUtils.getLoginUserId(request));
        errorLog.setUserType(WebFrameworkUtils.getLoginUserType(request));
        // 设置异常字段
        errorLog.setExceptionName(e.getClass().getName());
        errorLog.setExceptionMessage(ExceptionUtil.getMessage(e));
        errorLog.setExceptionRootCauseMessage(ExceptionUtil.getRootCauseMessage(e));
        errorLog.setExceptionStackTrace(ExceptionUtil.stacktraceToString(e));
        StackTraceElement[] stackTraceElements = e.getStackTrace();
        Assert.notEmpty(stackTraceElements, "异常 stackTraceElements 不能为空");
        StackTraceElement stackTraceElement = stackTraceElements[0];
        errorLog.setExceptionClassName(stackTraceElement.getClassName());
        errorLog.setExceptionFileName(stackTraceElement.getFileName());
        errorLog.setExceptionMethodName(stackTraceElement.getMethodName());
        errorLog.setExceptionLineNumber(stackTraceElement.getLineNumber());
        // 设置其它字段
        errorLog.setTraceId(TracerUtils.getTraceId());
        errorLog.setApplicationName("");
        errorLog.setRequestUrl(request.getRequestURI());
        Map<String, Object> requestParams = MapUtil.<String, Object>builder()
                .put("query", ServletUtils.getParamMap(request))
                .put("body", ServletUtils.getBody(request)).build();
        errorLog.setRequestParams(JsonUtils.toJsonString(requestParams));
        errorLog.setRequestMethod(request.getMethod());
        errorLog.setUserAgent(ServletUtils.getUserAgent(request));
        errorLog.setUserIp(ServletUtils.getClientIP(request));
        errorLog.setExceptionTime(LocalDateTime.now());
    }

    /**
     * 处理 Table 不存在的异常情况
     *
     * @param ex 异常
     * @return 如果是 Table 不存在的异常，则返回对应的 CommonResult
     */
    private CommonResult<?> handleTableNotExists(Throwable ex) {
        String message = ExceptionUtil.getRootCauseMessage(ex);
        if (!message.contains("doesn't exist")) {
            return null;
        }
        // 1. 数据报表
        if (message.contains("report_")) {
            log.error("[报表模块 yudao-module-report - 表结构未导入][参考 https://cloud.iocoder.cn/report/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[报表模块 yudao-module-report - 表结构未导入][参考 https://cloud.iocoder.cn/report/ 开启]");
        }
        // 2. 工作流
        if (message.contains("bpm_")) {
            log.error("[工作流模块 yudao-module-bpm - 表结构未导入][参考 https://cloud.iocoder.cn/bpm/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[工作流模块 yudao-module-bpm - 表结构未导入][参考 https://cloud.iocoder.cn/bpm/ 开启]");
        }
        // 3. 微信公众号
        if (message.contains("mp_")) {
            log.error("[微信公众号 yudao-module-mp - 表结构未导入][参考 https://cloud.iocoder.cn/mp/build/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[微信公众号 yudao-module-mp - 表结构未导入][参考 https://cloud.iocoder.cn/mp/build/ 开启]");
        }
        // 4. 商城系统
        if (StrUtil.containsAny(message, "product_", "promotion_", "trade_")) {
            log.error("[商城系统 yudao-module-mall - 已禁用][参考 https://cloud.iocoder.cn/mall/build/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[商城系统 yudao-module-mall - 已禁用][参考 https://cloud.iocoder.cn/mall/build/ 开启]");
        }
        // 5. ERP 系统
        if (message.contains("erp_")) {
            log.error("[ERP 系统 yudao-module-erp - 表结构未导入][参考 https://cloud.iocoder.cn/erp/build/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[ERP 系统 yudao-module-erp - 表结构未导入][参考 https://cloud.iocoder.cn/erp/build/ 开启]");
        }
        // 6. CRM 系统
        if (message.contains("crm_")) {
            log.error("[CRM 系统 yudao-module-crm - 表结构未导入][参考 https://cloud.iocoder.cn/crm/build/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[CRM 系统 yudao-module-crm - 表结构未导入][参考 https://cloud.iocoder.cn/crm/build/ 开启]");
        }
        // 7. 支付平台
        if (message.contains("pay_")) {
            log.error("[支付模块 yudao-module-pay - 表结构未导入][参考 https://cloud.iocoder.cn/pay/build/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[支付模块 yudao-module-pay - 表结构未导入][参考 https://cloud.iocoder.cn/pay/build/ 开启]");
        }
        // 8. AI 大模型
        if (message.contains("ai_")) {
            log.error("[AI 大模型 yudao-module-ai - 表结构未导入][参考 https://cloud.iocoder.cn/ai/build/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[AI 大模型 yudao-module-ai - 表结构未导入][参考 https://cloud.iocoder.cn/ai/build/ 开启]");
        }
        // 9. IOT 物联网
        if (message.contains("iot_")) {
            log.error("[IOT 物联网 yudao-module-iot - 表结构未导入][参考 https://doc.iocoder.cn/iot/build/ 开启]");
            return CommonResult.error(NOT_IMPLEMENTED.getCode(),
                    "[IOT 物联网 yudao-module-iot - 表结构未导入][参考 https://doc.iocoder.cn/iot/build/ 开启]");
        }
        return null;
    }

    /**
     * 构建详细异常信息
     */
    private CommonResult.ExceptionDetail buildExceptionDetail(HttpServletRequest request, Throwable ex) {
        if (ex == null) {
            return null;
        }

        CommonResult.ExceptionDetail detail = new CommonResult.ExceptionDetail();

        // 设置异常类型和消息
        detail.setExceptionType(ex.getClass().getName());
        detail.setExceptionMessage(ex.getMessage());

        // 设置请求URL
        if (request != null) {
            String requestUrl = request.getRequestURL().toString();
            String queryString = request.getQueryString();
            if (StrUtil.isNotBlank(queryString)) {
                requestUrl += "?" + queryString;
            }
            detail.setRequestUrl(requestUrl);
        }

        // 设置异常发生时间
        detail.setExceptionTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 构建交付支持信息
        detail.setDeliverySupport(buildDeliverySupport(request, ex));

        // 构建堆栈跟踪信息（前5层）
        StackTraceElement[] stackTrace = ex.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            List<CommonResult.ExceptionDetail.StackTraceInfo> stackTraceInfos = new ArrayList<>();
            int maxPrint = Math.min(5, stackTrace.length);
            for (int i = 0; i < maxPrint; i++) {
                StackTraceElement element = stackTrace[i];
                stackTraceInfos.add(new CommonResult.ExceptionDetail.StackTraceInfo(
                    element.getClassName(),
                    element.getMethodName(),
                    element.getFileName(),
                    element.getLineNumber()
                ));
            }
            detail.setStackTrace(stackTraceInfos);
        }

        // 处理根本原因异常
        Throwable cause = ex.getCause();
        if (cause != null && cause != ex) {
            StackTraceElement[] causeStackTrace = cause.getStackTrace();
            CommonResult.ExceptionDetail.StackTraceInfo firstStackTrace = null;
            if (causeStackTrace != null && causeStackTrace.length > 0) {
                StackTraceElement causeElement = causeStackTrace[0];
                firstStackTrace = new CommonResult.ExceptionDetail.StackTraceInfo(
                    causeElement.getClassName(),
                    causeElement.getMethodName(),
                    causeElement.getFileName(),
                    causeElement.getLineNumber()
                );
            }

            detail.setRootCause(new CommonResult.ExceptionDetail.CauseException(
                cause.getClass().getName(),
                cause.getMessage(),
                firstStackTrace
            ));
        }

        return detail;
    }

    /**
     * 构建交付支持信息
     */
    private CommonResult.ExceptionDetail.DeliverySupport buildDeliverySupport(HttpServletRequest request, Throwable ex) {
        CommonResult.ExceptionDetail.DeliverySupport support = new CommonResult.ExceptionDetail.DeliverySupport();

        // 分析错误严重程度
        support.setSeverity(analyzeSeverity(ex));

        // 识别业务模块
        support.setBusinessModule(identifyBusinessModule(request, ex));

        // 评估影响范围
        support.setImpactDescription(assessImpact(ex));

        // 提供解决方案
        support.setImmediateSolutions(getImmediateSolutions(ex));
        support.setWorkaroundSolutions(getWorkaroundSolutions(ex));

        // 设置联系信息
//        support.setContactInfo(getContactInfo(request, ex));

        // 提供文档链接
//        support.setDocumentLinks(getDocumentLinks(ex));

        // 收集环境信息
        support.setEnvironmentInfo(collectEnvironmentInfo());

        // 预估修复时间
//        support.setEstimatedFixTime(estimateFixTime(ex));

        // 判断是否可自助修复
        support.setCanSelfFix(canSelfFix(ex));
        if (support.getCanSelfFix()) {
            support.setSelfFixSteps(getSelfFixSteps(ex));
        }

        return support;
    }

    /**
     * 分析错误严重程度
     */
    private String analyzeSeverity(Throwable ex) {
        String message = ExceptionUtil.getRootCauseMessage(ex);
        String exceptionType = ex.getClass().getName();

        // CRITICAL: 系统崩溃级别
        if (exceptionType.contains("OutOfMemoryError") ||
            exceptionType.contains("StackOverflowError") ||
            message.contains("Connection refused") ||
            message.contains("服务不可用")) {
            return "CRITICAL";
        }

        // HIGH: 重要功能异常
        if (isDatabaseException(ex) ||
            exceptionType.contains("SQLException") ||
            message.contains("违反了非空约束") ||
            message.contains("duplicate key") ||
            message.contains("foreign key constraint")) {
            return "HIGH";
        }

        // MEDIUM: 一般功能问题
        if (exceptionType.contains("ValidationException") ||
            exceptionType.contains("IllegalArgumentException") ||
            message.contains("参数") ||
            message.contains("校验")) {
            return "MEDIUM";
        }

        // LOW: 轻微问题
        return "LOW";
    }

    /**
     * 识别业务模块
     */
    private String identifyBusinessModule(HttpServletRequest request, Throwable ex) {
        if (request != null) {
            String uri = request.getRequestURI();
            if (uri.contains("/acp/gj/")) return "监管业务";
            if (uri.contains("/acp/pm/")) return "人员管理";
            if (uri.contains("/system/")) return "系统管理";
            if (uri.contains("/infra/")) return "基础设施";
        }

        // 从异常堆栈分析
        StackTraceElement[] stackTrace = ex.getStackTrace();
        if (stackTrace != null && stackTrace.length > 0) {
            String className = stackTrace[0].getClassName();
            if (className.contains(".acp.gj.")) return "监管业务";
            if (className.contains(".acp.pm.")) return "人员管理";
            if (className.contains(".system.")) return "系统管理";
            if (className.contains(".infra.")) return "基础设施";
        }

        return "未知模块";
    }

    /**
     * 评估影响范围
     */
    private String assessImpact(Throwable ex) {
        String severity = analyzeSeverity(ex);
        String message = ExceptionUtil.getRootCauseMessage(ex);

        switch (severity) {
            case "CRITICAL":
                return "🔴 严重影响：系统核心功能不可用，影响所有用户正常使用";
            case "HIGH":
                if (message.contains("违反了非空约束")) {
                    return "🟠 高影响：数据录入功能异常，影响业务数据完整性";
                }
                if (message.contains("duplicate key")) {
                    return "🟠 高影响：数据重复冲突，影响数据录入操作";
                }
                return "🟠 高影响：重要业务功能异常，影响部分用户操作";
            case "MEDIUM":
                return "🟡 中等影响：一般功能问题，影响用户体验但不阻塞主要流程";
            case "LOW":
            default:
                return "🟢 轻微影响：小问题，对用户影响较小";
        }
    }

    /**
     * 获取立即解决方案
     */
    private List<String> getImmediateSolutions(Throwable ex) {
        List<String> solutions = new ArrayList<>();
        String message = ExceptionUtil.getRootCauseMessage(ex);

        if (message.contains("违反了非空约束")) {
            solutions.add("1. 检查必填字段是否都已填写");
            solutions.add("2. 确认前端表单验证是否正常工作");
            solutions.add("3. 检查数据传输过程中是否有字段丢失");
        } else if (message.contains("duplicate key")) {
            solutions.add("1. 检查是否重复提交了相同数据");
            solutions.add("2. 确认唯一性约束字段的值是否重复");
            solutions.add("3. 清理重复数据后重新操作");
        } else if (message.contains("foreign key constraint")) {
            solutions.add("1. 检查关联数据是否存在");
            solutions.add("2. 确认关联表中的数据没有被删除");
            solutions.add("3. 重新选择有效的关联数据");
        } else if (message.contains("Connection refused")) {
            solutions.add("1. 检查数据库服务是否正常运行");
            solutions.add("2. 确认网络连接是否正常");
            solutions.add("3. 重启应用服务");
        } else {
            solutions.add("1. 刷新页面重试");
            solutions.add("2. 检查输入数据是否正确");
            solutions.add("3. 联系技术支持");
        }

        return solutions;
    }

    /**
     * 获取临时绕过方案
     */
    private List<String> getWorkaroundSolutions(Throwable ex) {
        List<String> workarounds = new ArrayList<>();
        String message = ExceptionUtil.getRootCauseMessage(ex);

        if (message.contains("违反了非空约束")) {
            workarounds.add("1. 暂时填写默认值或占位符");
            workarounds.add("2. 使用其他录入方式（如批量导入）");
            workarounds.add("3. 联系管理员临时调整字段约束");
        } else if (message.contains("duplicate key")) {
            workarounds.add("1. 修改重复的字段值");
            workarounds.add("2. 先删除冲突数据再重新添加");
            workarounds.add("3. 使用更新操作替代新增操作");
        } else if (message.contains("Connection refused")) {
            workarounds.add("1. 等待系统恢复后重试");
            workarounds.add("2. 使用离线模式记录数据");
            workarounds.add("3. 切换到备用系统");
        } else {
            workarounds.add("1. 尝试使用不同的浏览器");
            workarounds.add("2. 清除浏览器缓存后重试");
            workarounds.add("3. 使用移动端应用操作");
        }

        return workarounds;
    }

    /**
     * 获取联系信息
     */
    private CommonResult.ExceptionDetail.ContactInfo getContactInfo(HttpServletRequest request, Throwable ex) {
        CommonResult.ExceptionDetail.ContactInfo contact = new CommonResult.ExceptionDetail.ContactInfo();

        String businessModule = identifyBusinessModule(request, ex);
        switch (businessModule) {
            case "监管业务":
                contact.setPrimaryContact("张三 (监管业务负责人)");
                contact.setContactMethod("电话: 138-0000-0001, 微信: zhangsan_gj");
                contact.setEmergencyContact("李四 (技术经理): 138-0000-0002");
                contact.setTeam("监管业务开发团队");
                break;
            case "人员管理":
                contact.setPrimaryContact("王五 (人员管理负责人)");
                contact.setContactMethod("电话: 138-0000-0003, 微信: wangwu_pm");
                contact.setEmergencyContact("赵六 (技术经理): 138-0000-0004");
                contact.setTeam("人员管理开发团队");
                break;
            case "系统管理":
                contact.setPrimaryContact("孙七 (系统管理负责人)");
                contact.setContactMethod("电话: 138-0000-0005, 微信: sunqi_sys");
                contact.setEmergencyContact("周八 (技术经理): 138-0000-0006");
                contact.setTeam("系统管理开发团队");
                break;
            default:
                contact.setPrimaryContact("技术支持");
                contact.setContactMethod("电话: 400-000-0000, 邮箱: <EMAIL>");
                contact.setEmergencyContact("值班经理: 138-0000-9999");
                contact.setTeam("技术支持团队");
        }

        return contact;
    }

    /**
     * 获取相关文档链接
     */
    private List<String> getDocumentLinks(Throwable ex) {
        List<String> links = new ArrayList<>();
        String message = ExceptionUtil.getRootCauseMessage(ex);

        if (message.contains("违反了非空约束") || message.contains("数据")) {
            links.add("📖 数据录入规范: http://wiki.company.com/data-input-guide");
            links.add("🔧 数据库约束说明: http://wiki.company.com/database-constraints");
        }

        if (message.contains("Connection") || message.contains("连接")) {
            links.add("🌐 网络连接故障排查: http://wiki.company.com/network-troubleshooting");
            links.add("🔧 服务重启指南: http://wiki.company.com/service-restart-guide");
        }

        // 通用文档
        links.add("📚 常见问题FAQ: http://wiki.company.com/faq");
        links.add("🆘 紧急处理流程: http://wiki.company.com/emergency-process");

        return links;
    }

    /**
     * 收集环境信息
     */
    private CommonResult.ExceptionDetail.EnvironmentInfo collectEnvironmentInfo() {
        CommonResult.ExceptionDetail.EnvironmentInfo envInfo = new CommonResult.ExceptionDetail.EnvironmentInfo();

        try {
            // 服务器环境
            String env = System.getProperty("spring.profiles.active", "unknown");
            envInfo.setServerEnvironment(env);

            // 应用版本 (可以从配置文件或manifest中获取)
            envInfo.setApplicationVersion("v2.1.0"); // 这里应该从实际配置中获取

            // 数据库状态 (简化版，实际应该检查数据库连接)
            envInfo.setDatabaseStatus("需要检查");

            // 相关服务状态
            Map<String, String> serviceStatus = new HashMap<>();
            serviceStatus.put("Redis", "需要检查");
            serviceStatus.put("消息队列", "需要检查");
            serviceStatus.put("文件服务", "需要检查");
            envInfo.setServiceStatus(serviceStatus);

            // 系统负载 (简化版)
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            double memoryUsage = (double) usedMemory / totalMemory * 100;
            envInfo.setSystemLoad(String.format("内存使用率: %.1f%%", memoryUsage));

        } catch (Exception e) {
            log.warn("收集环境信息失败", e);
            envInfo.setServerEnvironment("获取失败");
            envInfo.setSystemLoad("获取失败");
        }

        return envInfo;
    }

    /**
     * 预估修复时间
     */
    private String estimateFixTime(Throwable ex) {
        String severity = analyzeSeverity(ex);
        String message = ExceptionUtil.getRootCauseMessage(ex);

        switch (severity) {
            case "CRITICAL":
                return "⚡ 紧急处理：1-2小时内";
            case "HIGH":
                if (message.contains("违反了非空约束") || message.contains("duplicate key")) {
                    return "🔧 快速修复：30分钟内";
                }
                return "⏰ 优先处理：2-4小时内";
            case "MEDIUM":
                return "📅 计划修复：1-2个工作日";
            case "LOW":
            default:
                return "📋 后续处理：3-5个工作日";
        }
    }

    /**
     * 判断是否可自助修复
     */
    private Boolean canSelfFix(Throwable ex) {
        String message = ExceptionUtil.getRootCauseMessage(ex);

        // 这些问题通常可以自助解决
        return message.contains("违反了非空约束") ||
               message.contains("duplicate key") ||
               message.contains("参数") ||
               message.contains("校验") ||
               ex instanceof IllegalArgumentException ||
               ex instanceof ValidationException;
    }

    /**
     * 获取自助修复步骤
     */
    private List<String> getSelfFixSteps(Throwable ex) {
        List<String> steps = new ArrayList<>();
        String message = ExceptionUtil.getRootCauseMessage(ex);

        if (message.contains("违反了非空约束")) {
            steps.add("✅ 第1步：检查表单中的必填字段");
            steps.add("✅ 第2步：确保所有红色标记的字段都已填写");
            steps.add("✅ 第3步：重新提交表单");
            steps.add("✅ 第4步：如果仍有问题，联系技术支持");
        } else if (message.contains("duplicate key")) {
            steps.add("✅ 第1步：检查是否重复提交了相同数据");
            steps.add("✅ 第2步：修改重复的字段值（如编号、名称等）");
            steps.add("✅ 第3步：重新提交");
            steps.add("✅ 第4步：如果确认数据无重复仍报错，联系技术支持");
        } else if (message.contains("参数") || message.contains("校验")) {
            steps.add("✅ 第1步：检查输入数据格式是否正确");
            steps.add("✅ 第2步：确认必填项都已填写");
            steps.add("✅ 第3步：检查数据长度是否超限");
            steps.add("✅ 第4步：重新提交");
        } else {
            steps.add("✅ 第1步：刷新页面重试");
            steps.add("✅ 第2步：检查网络连接");
            steps.add("✅ 第3步：清除浏览器缓存");
            steps.add("✅ 第4步：联系技术支持");
        }

        return steps;
    }

}
