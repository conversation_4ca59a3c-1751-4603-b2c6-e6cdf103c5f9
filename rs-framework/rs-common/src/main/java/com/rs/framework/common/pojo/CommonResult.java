package com.rs.framework.common.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.rs.framework.common.exception.ErrorCode;
import com.rs.framework.common.exception.ServiceException;
import com.rs.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.rs.framework.common.exception.util.ServiceExceptionUtil;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 通用返回
 *
 * @param <T> 数据泛型
 * <AUTHOR>
 */
@Data
public class CommonResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

	/**
     * 错误码
     *
     * @see ErrorCode#getCode()
     */
    private Integer code;
    private Integer returnCode=0;
    private Integer status;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 错误提示，用户可阅读
     *
     * @see ErrorCode#getMsg() ()
     */
    private String msg;
    private String message;

    /**
     * 详细异常信息，用于开发调试
     */
    private ExceptionDetail exceptionDetail;

    /**
     * 是否成功
     */
    @SuppressWarnings("unused")
	private Boolean success;

    /**
     * 将传入的 result 对象，转换成另外一个泛型结果的对象
     *
     * 因为 A 方法返回的 CommonResult 对象，不满足调用其的 B 方法的返回，所以需要进行转换。
     *
     * @param result 传入的 result 对象
     * @param <T> 返回的泛型
     * @return 新的 CommonResult 对象
     */
    public static <T> CommonResult<T> error(CommonResult<?> result) {
        return error(result.getCode(), result.getMsg());
    }
    public static <T> CommonResult<T> error(String result) {
        return error(500, result);
    }
    public static <T> CommonResult<T> error(T data) {
        CommonResult<T> result = new CommonResult<>();
        result.code = 500;
        result.data = data;
        result.msg = "";
        result.returnCode = 1;
        return result;
    }
    public static <T> CommonResult<T> error(Integer code,T data) {
        CommonResult<T> result = new CommonResult<>();
        result.code = code;
        result.data = data;
        result.msg = "";
        result.returnCode = 1;
        return result;
    }

    public static <T> CommonResult<T> error(Integer code, String message) {
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(code))  {
            throw new IllegalArgumentException("code 必须是错误的！");
        }
        CommonResult<T> result = new CommonResult<>();
        result.code = code;
        result.msg = message;
        result.message = message;
        result.success = false;
        result.returnCode = 1;
        return result;
    }

    public static <T> CommonResult<T> error(ErrorCode errorCode, Object... params) {
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(errorCode.getCode()))  {
            throw new IllegalArgumentException("code 必须是错误的！");
        }
        CommonResult<T> result = new CommonResult<>();
        result.code = errorCode.getCode();
        result.msg = ServiceExceptionUtil.doFormat(errorCode.getCode(), errorCode.getMsg(), params);
        result.success = false;
        result.returnCode = 1;
        return result;
    }

    public static <T> CommonResult<T> error(ErrorCode errorCode) {
        return error(errorCode.getCode(), errorCode.getMsg());
    }

    public static <T> CommonResult<T> success(T data, String msg) {
        CommonResult<T> result = new CommonResult<>();
        result.code = GlobalErrorCodeConstants.SUCCESS.getCode();
        result.status = 200;
        result.data = data;
        result.msg = msg;
        result.message = msg;
        result.success = true;
        result.returnCode = 0;
        return result;
    }

    public static <T> CommonResult<T> success(T data) {
    	return success(data, "");
    }

    @SuppressWarnings("rawtypes")
	public static CommonResult success() {
    	return success("请求成功", "");
    }

    public static boolean isSuccess(Integer code) {
        return Objects.equals(code, GlobalErrorCodeConstants.SUCCESS.getCode());
    }

    public boolean isSuccess() {
        return isSuccess(code);
    }

    @JsonIgnore // 避免 jackson 序列化
    public boolean isError() {
        return !isSuccess();
    }

    // ========= 和 Exception 异常体系集成 =========

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     */
    public void checkError() throws ServiceException {
        if (isSuccess()) {
            return;
        }
        // 业务异常
        throw new ServiceException(code, msg);
    }

    /**
     * 判断是否有异常。如果有，则抛出 {@link ServiceException} 异常
     * 如果没有，则返回 {@link #data} 数据
     */
    @JsonIgnore // 避免 jackson 序列化
    public T getCheckedData() {
        checkError();
        return data;
    }

    public static <T> CommonResult<T> error(ServiceException serviceException) {
        return error(serviceException.getCode(), serviceException.getMessage());
    }

    /**
     * 创建带有详细异常信息的错误结果
     */
    public static <T> CommonResult<T> error(Integer code, String message, ExceptionDetail exceptionDetail) {
        CommonResult<T> result = error(code, message);
        result.exceptionDetail = exceptionDetail;
        return result;
    }

    public static ResponseEntity<byte[]> fileStream(byte[] bot, String fileName, MediaType mediaType) {
        HttpHeaders headers = new HttpHeaders();
        // 正确设置 Content-Disposition，区分预览与下载

        if (MediaType.APPLICATION_OCTET_STREAM.equals(mediaType)) {
            headers.setContentDisposition(
                    org.springframework.http.ContentDisposition
                            .attachment()
                            .filename(fileName, java.nio.charset.StandardCharsets.UTF_8)
                            .build()
            );
        } else {
            headers.setContentDisposition(
                    org.springframework.http.ContentDisposition
                            .inline()
                            .filename(fileName, java.nio.charset.StandardCharsets.UTF_8)
                            .build()
            );
        }
        headers.setContentType(mediaType);
        headers.add("X-Frame-Options", "ALLOWALL");
        return ResponseEntity
                .ok()
                .headers(headers)
                .contentLength(bot.length)
                .contentType(mediaType)
                .body(bot);
    }

    /**
     * 异常详细信息类
     */
    @Data
    public static class ExceptionDetail implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 异常类型
         */
        private String exceptionType;

        /**
         * 异常消息
         */
        private String exceptionMessage;

        /**
         * 请求URL
         */
        private String requestUrl;

        /**
         * 异常发生时间
         */
        private String exceptionTime;

        /**
         * 异常堆栈信息（前几层）
         */
        private List<StackTraceInfo> stackTrace;

        /**
         * 根本原因异常
         */
        private CauseException rootCause;

        /**
         * 交付支持信息
         */
        private DeliverySupport deliverySupport;

        /**
         * 堆栈跟踪信息
         */
        @Data
        public static class StackTraceInfo implements Serializable {
            private static final long serialVersionUID = 1L;

            /**
             * 类名
             */
            private String className;

            /**
             * 方法名
             */
            private String methodName;

            /**
             * 文件名
             */
            private String fileName;

            /**
             * 行号
             */
            private Integer lineNumber;

            public StackTraceInfo(String className, String methodName, String fileName, Integer lineNumber) {
                this.className = className;
                this.methodName = methodName;
                this.fileName = fileName;
                this.lineNumber = lineNumber;
            }
        }

        /**
         * 根本原因异常信息
         */
        @Data
        public static class CauseException implements Serializable {
            private static final long serialVersionUID = 1L;

            /**
             * 异常类型
             */
            private String exceptionType;

            /**
             * 异常消息
             */
            private String exceptionMessage;

            /**
             * 第一层堆栈信息
             */
            private StackTraceInfo firstStackTrace;

            public CauseException(String exceptionType, String exceptionMessage, StackTraceInfo firstStackTrace) {
                this.exceptionType = exceptionType;
                this.exceptionMessage = exceptionMessage;
                this.firstStackTrace = firstStackTrace;
            }
        }

        /**
         * 交付支持信息类
         */
        @Data
        public static class DeliverySupport implements Serializable {
            private static final long serialVersionUID = 1L;

            /**
             * 错误严重程度
             * CRITICAL: 系统崩溃，影响核心功能
             * HIGH: 重要功能异常，需要立即处理
             * MEDIUM: 一般功能问题，可以计划处理
             * LOW: 轻微问题，不影响主要功能
             */
            private String severity;

            /**
             * 业务模块
             */
            private String businessModule;

            /**
             * 影响范围描述
             */
            private String impactDescription;

            /**
             * 立即解决方案
             */
            private List<String> immediateSolutions;

            /**
             * 临时绕过方案
             */
            private List<String> workaroundSolutions;

            /**
             * 需要联系的技术人员
             */
            private ContactInfo contactInfo;

            /**
             * 相关文档链接
             */
            private List<String> documentLinks;

            /**
             * 环境信息
             */
            private EnvironmentInfo environmentInfo;

            /**
             * 预估修复时间
             */
            private String estimatedFixTime;

            /**
             * 是否可自助修复
             */
            private Boolean canSelfFix;

            /**
             * 自助修复步骤
             */
            private List<String> selfFixSteps;
        }

        /**
         * 联系人信息
         */
        @Data
        public static class ContactInfo implements Serializable {
            private static final long serialVersionUID = 1L;

            /**
             * 主要负责人
             */
            private String primaryContact;

            /**
             * 联系方式
             */
            private String contactMethod;

            /**
             * 紧急联系人
             */
            private String emergencyContact;

            /**
             * 团队/部门
             */
            private String team;
        }

        /**
         * 环境信息
         */
        @Data
        public static class EnvironmentInfo implements Serializable {
            private static final long serialVersionUID = 1L;

            /**
             * 服务器环境
             */
            private String serverEnvironment;

            /**
             * 应用版本
             */
            private String applicationVersion;

            /**
             * 数据库状态
             */
            private String databaseStatus;

            /**
             * 相关服务状态
             */
            private Map<String, String> serviceStatus;

            /**
             * 系统负载
             */
            private String systemLoad;
        }
    }

}
