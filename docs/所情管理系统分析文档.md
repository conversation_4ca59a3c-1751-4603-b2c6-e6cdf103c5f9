# 所情管理系统分析文档

## 1. 系统概述

所情管理系统是监管场所内事件管理的核心模块，负责处理从事件发生、登记、处置到最终办结的完整流程。系统支持多种事件源的自动触发和手动登记，通过规范化的三级处置流程确保每个事件都能得到及时、有效的处理。

## 2. 核心实体结构

### 2.1 所情登记 (SqglSqdjDO)
**表名**: `acp_pi_sqgl_sqdj`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 主键 |
| eventCode | String | 所情编号（自动生成） |
| eventLevel | String | 所情等级（1-4级，4级最低） |
| eventSrc | String | 所情来源（见事件源分类） |
| status | String | 状态（0:待核实, 1:待处置, 2:待审批, 3:已办结） |
| eventTemplateId | String | 所情模板ID |
| eventName | String | 所情名称 |
| eventType | String | 所情类型 |
| areaId | String | 所情地点ID |
| areaName | String | 所情地点名称 |
| eventStartTime | Date | 所情开始时间 |
| eventEndTime | Date | 所情结束时间 |
| eventDetails | String | 所情详情 |
| handleUserName | String | 巡控岗处置人名称 |

### 2.2 所情处置 (SqglSqczDO)
**表名**: `acp_pi_sqgl_sqcz`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 主键 |
| sqdjId | String | 所情登记ID |
| handleType | String | 处置类型（1:巡控岗, 2:中间环节, 3:所领导审批） |
| handlePostCode | String | 处置岗位编号 |
| handlePostName | String | 处置岗位名称 |
| handleUserSfzh | String | 处置人身份证号 |
| handleUserName | String | 处置人名称 |
| handleTime | Date | 处置时间 |
| handleInfo | String | 处置情况 |
| status | String | 处置状态（0:未办结, 1:已办结, 2:不通过, 3:通过） |
| hanleGenericPlan | String | 处置预案 |
| attUrl | String | 附件地址 |
| approveInfo | String | 审批情况（JSON格式） |

### 2.3 推送对象 (SqglSqdjtsdxDO)
**表名**: `acp_pi_sqgl_sqdjtsdx`

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | String | 主键 |
| sqdjId | String | 所情登记ID |
| sqczId | String | 所情处置ID |
| pushUserSfzh | String | 推送用户身份证号 |
| pushUserName | String | 推送用户名称 |
| pushPostCode | String | 推送岗位编号 |
| pushPostName | String | 推送岗位名称 |
| pushTime | Date | 推送时间 |

## 3. 事件源分类

### 3.1 支持的事件源类型

| 事件源代码 | 事件源名称 | 触发方式 | 关联信息 |
|-----------|-----------|----------|----------|
| 001 | 所情登记 | 手动录入 | 巡控岗人员手动创建 |
| 002 | 视频分析 | 自动触发 | 通过第三方设备ID关联区域 |
| 003 | 手环告警 | 自动触发 | 通过被监管人员编码关联人员信息 |
| 006 | 周界告警 | 自动触发 | 通过设备编码关联周界设备和区域 |
| 010 | 快鱼音频 | 自动触发 | 通过音频设备关联房间区域 |

### 3.2 自动触发逻辑

#### 手环告警处理
```java
// 处理手环告警，关联被监管人员信息
if("003".equals(sqglSqdj.getEventSrc()) && ObjectUtil.isNotEmpty(createReqVO.getBjgrybm())){
    PrisonerVwRespVO prisonerVwRespVO = prisonerService.getPrisonerByJgrybm(createReqVO.getBjgrybm());
    // 创建关联人员记录
}
```

#### 视频分析告警处理
```java
// 处理视频分析告警，关联区域信息
if ("002".equals(sqglSqdj.getEventSrc()) && ObjectUtil.isNotEmpty(createReqVO.getThirdDeviceId())) {
    List<JSONObject> areaList = sqglSqdjDao.getAreaByGbcode(createReqVO.getThirdDeviceId());
    // 设置区域信息
}
```

## 4. 处置流程

### 4.1 三级处置架构

```mermaid
graph TD
    A[事件触发] --> B[创建待核实所情]
    B --> C[巡控岗核实]
    C --> D{核实结果}
    D -->|直接办结| E[已办结]
    D -->|需要处置| F[创建中间环节]
    F --> G[并行处置节点]
    G --> H{所有节点完成?}
    H -->|否| G
    H -->|是| I[创建所领导审批]
    I --> J[所领导审批]
    J --> K{审批结果}
    K -->|通过| E
    K -->|不通过| L[退回重新处置]
    L --> F
```

### 4.2 岗位编码说明

| 岗位编码 | 岗位名称 | 处置类型 | 说明 |
|---------|---------|----------|------|
| 02 | 巡控岗 | 1 | 首次核实环节 |
| 03 | 管控岗 | 2 | 中间处置环节 |
| 04 | 安全岗 | 2 | 中间处置环节 |
| 06 | 所领导 | 3 | 最终审批环节 |

### 4.3 状态流转

#### 所情登记状态
- **0**: 待核实 - 等待巡控岗核实
- **1**: 待处置 - 等待中间环节处置
- **2**: 待审批 - 等待所领导审批
- **3**: 已办结 - 流程结束

#### 处置状态
- **0**: 未办结 - 等待处置
- **1**: 已办结 - 处置完成
- **2**: 不通过 - 审批不通过
- **3**: 通过 - 审批通过

## 5. 核心业务方法

### 5.1 创建待核实所情
```java
public void createWaitVerify(String orgCode, String orgName, SqglSqdjSaveReqVO createReqVO)
```
**功能**: 外部系统触发创建待核实所情
**流程**:
1. 创建所情登记记录
2. 根据事件源和告警类型查找模板配置
3. 根据不同事件源关联相关信息
4. 创建巡控岗处置记录
5. 创建推送对象列表
6. 发送WebSocket消息和待办消息

### 5.2 巡控岗核实
```java
public Boolean verify(SqglSqdjSaveReqVO updateReqVO)
```
**功能**: 巡控岗核实所情
**流程**:
1. 更新关联人员信息
2. 更新巡控岗处置状态
3. 判断是否直接办结或转入中间环节
4. 创建中间环节处置记录

### 5.3 中间环节处置
```java
public Boolean dispose(SqglSqczSaveReqVO updateReqVO)
```
**功能**: 中间环节处置所情
**流程**:
1. 更新处置信息
2. 判断是否为最后一个处置节点
3. 如果是最后节点，创建所领导审批环节
4. 消除对应待办消息

### 5.4 所领导审批
```java
public Boolean approve(SqglSqczApproveSaveReqVO updateReqVO)
```
**功能**: 所领导审批所情处置结果
**流程**:
1. 处理审批结果
2. 如果通过，办结所情
3. 如果不通过，重新创建处置环节
4. 发送相应消息通知

## 6. 消息推送机制

### 6.1 WebSocket实时推送
**触发条件**: 报警联动设置中包含"sqtc"（所情推出）
**推送内容**:
```json
{
    "businessType": "sqdj",
    "sqdjId": "所情ID",
    "eventLevel": "事件级别",
    "happenTime": "发生时间",
    "eventDetails": "事件详情",
    "url": "处理页面URL",
    "status": "当前状态"
}
```

### 6.2 待办消息推送
**消息类型**: 
- `108_001`: 所情处置待办
- `ACP_JJKS_SQCZDB`: 所情处置待办模块
- `ACP_JJKS_SQSHDB`: 所情审核待办模块

## 7. 配置管理

### 7.1 模板配置 (SqglMbpzDO)
**功能**: 根据事件源和告警类型预配置处置流程
**配置内容**:
- 推送对象设置
- 处置预案
- 处置业务类型

### 7.2 报警联动设置 (SqglBjldszDO)
**功能**: 控制告警联动行为
**配置项**:
- 是否启用WebSocket推送
- 提示音设置
- 处理时效设置

## 8. 关键业务规则

### 8.1 并行处置逻辑
中间环节支持多个岗位并行处置，只有当所有并行节点都完成处置后，才会进入所领导审批环节。

### 8.2 审批退回机制
所领导审批不通过时，系统会重新创建对应的中间环节处置记录，实现流程回退。

### 8.3 超时处理
系统支持处置超时检测，可配置不同环节的处理时效。

## 9. 扩展点

### 9.1 新增事件源
1. 在事件源枚举中添加新的类型
2. 在`createWaitVerify`方法中添加对应的处理逻辑
3. 配置相应的模板和联动设置

### 9.2 新增处置环节
1. 定义新的岗位编码
2. 在模板配置中添加新的处置岗位
3. 更新处置流程逻辑

## 10. 注意事项

1. **事务管理**: 所有核心业务方法都使用`@Transactional`注解确保数据一致性
2. **并发控制**: 通过数据库锁和状态检查避免并发问题
3. **消息可靠性**: WebSocket消息发送失败不影响主流程
4. **数据完整性**: 关联数据的创建和更新保持同步

## 11. 技术实现细节

### 11.1 数据库设计

#### 主要表结构关系
```sql
-- 所情登记主表
acp_pi_sqgl_sqdj (所情登记)
├── acp_pi_sqgl_sqcz (所情处置) [1:N]
│   ├── acp_pi_sqgl_sqdjtsdx (推送对象) [1:N]
│   └── acp_pi_sqgl_sqcz_gywl (关联业务) [1:N]
├── acp_pi_sqgl_sqdj_glry (关联人员) [1:N]
└── acp_pi_sqgl_mbpz (模板配置) [N:1]
```

#### 索引建议
```sql
-- 所情登记表索引
CREATE INDEX idx_sqdj_status ON acp_pi_sqgl_sqdj(status);
CREATE INDEX idx_sqdj_event_src ON acp_pi_sqgl_sqdj(event_src);
CREATE INDEX idx_sqdj_org_code ON acp_pi_sqgl_sqdj(org_code);
CREATE INDEX idx_sqdj_happen_time ON acp_pi_sqgl_sqdj(happen_time);

-- 所情处置表索引
CREATE INDEX idx_sqcz_sqdj_id ON acp_pi_sqgl_sqcz(sqdj_id);
CREATE INDEX idx_sqcz_handle_type ON acp_pi_sqgl_sqcz(handle_type);
CREATE INDEX idx_sqcz_status ON acp_pi_sqgl_sqcz(status);

-- 推送对象表索引
CREATE INDEX idx_tsdx_sqdj_id ON acp_pi_sqgl_sqdjtsdx(sqdj_id);
CREATE INDEX idx_tsdx_sqcz_id ON acp_pi_sqgl_sqdjtsdx(sqcz_id);
CREATE INDEX idx_tsdx_user_sfzh ON acp_pi_sqgl_sqdjtsdx(push_user_sfzh);
```

### 11.2 关键算法实现

#### 并行处置完成检测算法
```java
private boolean isLastMiddleNode(String sqdjId, String currentSqczId) {
    // 查询所有中间环节处置记录
    LambdaQueryWrapper<SqglSqczDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(SqglSqczDO::getSqdjId, sqdjId)
           .eq(SqglSqczDO::getHandleType, "2");

    List<SqglSqczDO> allMiddleNodes = sqglSqczService.list(wrapper);

    // 检查除当前节点外是否还有未完成的节点
    for(SqglSqczDO node : allMiddleNodes) {
        if(!currentSqczId.equals(node.getId()) && "0".equals(node.getStatus())) {
            return false; // 还有未完成的节点
        }
    }
    return true; // 当前是最后一个完成的节点
}
```

#### 节点进度计算算法
```java
private List<JSONObject> createNodeProgress(SqglSqdjDO sqglSqdjDO, List<SqglSqczDO> sqczDOList) {
    // 按处置类型和岗位分组，取最新记录
    Map<String, SqglSqczDO> latestNodeMap = sqczDOList.stream()
        .collect(Collectors.toMap(
            node -> node.getHandlePostCode() + ":" + node.getHandleType(),
            Function.identity(),
            (existing, replacement) ->
                replacement.getAddTime().after(existing.getAddTime()) ? replacement : existing
        ));

    // 构建进度节点列表
    List<JSONObject> progressNodes = new ArrayList<>();
    // ... 具体实现逻辑
    return progressNodes;
}
```

### 11.3 性能优化策略

#### 批量操作优化
```java
// 批量保存推送对象
if(CollectionUtil.isNotEmpty(tsdxList)) {
    sqglSqdjtsdxService.saveBatch(tsdxList, 1000); // 分批保存，每批1000条
}

// 批量更新处置状态
LambdaUpdateWrapper<SqglSqczDO> updateWrapper = new LambdaUpdateWrapper<>();
updateWrapper.in(SqglSqczDO::getId, sqczIdList)
             .set(SqglSqczDO::getStatus, "1")
             .set(SqglSqczDO::getHandleTime, new Date());
sqglSqczService.update(updateWrapper);
```

#### 缓存策略
```java
// 字典数据缓存
@Cacheable(value = "dict", key = "#dictType + ':' + #dictCode")
public String getDictLabel(String dictType, String dictCode) {
    return DicUtils.translate(dictType, dictCode);
}

// 用户信息缓存
@Cacheable(value = "user", key = "#orgCode + ':' + #postCode")
public List<UserRespDTO> getUserByOrgAndPost(String orgCode, String postCode) {
    return userApi.getUserByOrgAndPost(orgCode, postCode);
}
```

### 11.4 异常处理机制

#### 业务异常处理
```java
public class SqglBusinessException extends RuntimeException {
    private String errorCode;
    private String errorMessage;

    public SqglBusinessException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }
}

// 使用示例
if(CollectionUtil.isEmpty(userList)) {
    throw new SqglBusinessException("SQGL_001", "所领导岗位下未配置用户，请配置用户后再提交");
}
```

#### 消息发送异常处理
```java
private void sendWebSocketMessage(JSONObject msg, List<SqglSqdjtsdxDO> userList) {
    for(SqglSqdjtsdxDO user : userList) {
        try {
            WebSocketServer.sendInfo(msg.toJSONString(), user.getPushUserSfzh());
            log.info("WebSocket消息发送成功，用户：{}", user.getPushUserName());
        } catch (Exception e) {
            log.error("WebSocket消息发送失败，用户：{}，错误：{}", user.getPushUserName(), e.getMessage());
            // 记录失败日志，但不影响主流程
        }
    }
}
```

### 11.5 监控和日志

#### 关键操作日志
```java
@Slf4j
public class SqglSqdjServiceImpl {

    public void createWaitVerify(String orgCode, String orgName, SqglSqdjSaveReqVO createReqVO) {
        log.info("开始创建待核实所情登记，机构：{}，事件源：{}，告警类型：{}",
                orgName, createReqVO.getEventSrc(), createReqVO.getAlarmType());

        try {
            // 业务逻辑
            log.info("所情登记创建成功，所情ID：{}，所情编码：{}", sqglSqdj.getId(), sqglSqdj.getEventCode());
        } catch (Exception e) {
            log.error("所情登记创建失败，机构：{}，错误：{}", orgName, e.getMessage(), e);
            throw e;
        }
    }
}
```

#### 性能监控
```java
@Component
public class SqglPerformanceMonitor {

    @EventListener
    public void handleSqglCreated(SqglCreatedEvent event) {
        long processingTime = event.getEndTime() - event.getStartTime();
        if(processingTime > 5000) { // 超过5秒记录警告
            log.warn("所情创建耗时过长：{}ms，所情ID：{}", processingTime, event.getSqdjId());
        }
    }
}
```

## 12. 部署和运维

### 12.1 配置参数
```yaml
# application.yml
sqgl:
  # WebSocket推送配置
  websocket:
    enabled: true
    max-connections: 1000

  # 消息推送配置
  message:
    batch-size: 100
    retry-times: 3
    timeout: 30000

  # 处置时效配置
  timeout:
    verify: 30      # 核实时效（分钟）
    dispose: 60     # 处置时效（分钟）
    approve: 120    # 审批时效（分钟）
```

### 12.2 健康检查
```java
@Component
public class SqglHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        try {
            // 检查数据库连接
            sqglSqdjDao.selectCount(null);

            // 检查WebSocket服务
            if(WebSocketServer.getOnlineCount() >= 0) {
                return Health.up()
                    .withDetail("database", "UP")
                    .withDetail("websocket", "UP")
                    .withDetail("online-users", WebSocketServer.getOnlineCount())
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
        return Health.down().build();
    }
}
```

---

**文档版本**: 1.0
**最后更新**: 2024年
**维护人员**: 系统开发团队
