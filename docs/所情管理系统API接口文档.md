# 所情管理系统API接口文档

## 1. 接口概述

所情管理系统提供完整的RESTful API接口，支持所情的创建、查询、处置、审批等全流程操作。

## 2. 基础信息

- **Base URL**: `/acp/pi/sqgl-sqdj`
- **Content-Type**: `application/json`
- **认证方式**: Bearer <PERSON>
- **字符编码**: UTF-8

## 3. 核心接口

### 3.1 创建所情登记

**接口地址**: `POST /create`

**接口描述**: 手动创建所情登记记录

**请求参数**:
```json
{
    "eventLevel": "3",                    // 所情等级（1-4级）
    "areaId": "area001",                  // 所情地点ID
    "areaName": "监室A区",                 // 所情地点名称
    "eventTemplateId": "template001",     // 所情模板ID
    "eventName": "打架斗殴",               // 所情名称
    "eventType": "001",                   // 所情类型
    "eventStartTime": "2024-01-01 10:00:00", // 所情开始时间
    "eventEndTime": "2024-01-01 10:30:00",   // 所情结束时间
    "eventDetails": "监室内发生打架事件",    // 所情详情
    "handleInfo": "已现场处置",            // 处置情况
    "tsdxList": [                         // 推送对象列表
        {
            "pushPostCode": "03",         // 推送岗位编码
            "pushUserSfzh": "110101199001011234", // 推送用户身份证
            "pushUserName": "张三"         // 推送用户姓名
        }
    ],
    "jgryList": [                         // 监管人员列表
        {
            "personnelId": "prisoner001",  // 人员ID
            "personnelName": "李四",       // 人员姓名
            "roomId": "room001",          // 房间ID
            "roomName": "101监室"         // 房间名称
        }
    ]
}
```

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": "sqdj_uuid_001"  // 返回所情登记ID
}
```

### 3.2 创建待核实所情

**接口地址**: `POST /createWaitVerify`

**接口描述**: 外部系统自动创建待核实所情

**请求参数**:
```json
{
    "eventSrc": "003",                    // 事件源（003:手环告警）
    "alarmType": "001",                   // 告警类型
    "eventLevel": "2",                    // 所情等级
    "eventStartTime": "2024-01-01 10:00:00", // 发生时间
    "eventDetails": "手环异常告警",        // 事件详情
    "bjgrybm": "prisoner001",             // 被监管人员编码（手环告警时使用）
    "thirdDeviceId": "device001",         // 第三方设备ID（视频/周界告警时使用）
    "orgCode": "org001",                  // 机构代码
    "orgName": "某某看守所"               // 机构名称
}
```

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": null
}
```

### 3.3 巡控岗核实

**接口地址**: `POST /verify`

**接口描述**: 巡控岗核实所情

**请求参数**:
```json
{
    "id": "sqdj_uuid_001",               // 所情登记ID
    "saveType": "1",                      // 保存类型（1:转处置, 2:直接办结）
    "handleInfo": "核实情况说明",          // 处置情况
    "eventLevel": "3",                    // 所情等级
    "eventName": "打架斗殴",               // 所情名称
    "eventDetails": "详细情况描述",        // 所情详情
    "tsdxList": [                         // 推送对象列表（转处置时必填）
        {
            "pushPostCode": "03",         // 推送岗位编码
            "pushUserSfzh": "110101199001011234", // 推送用户身份证
            "pushUserName": "张三"         // 推送用户姓名
        }
    ]
}
```

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 3.4 中间环节处置

**接口地址**: `POST /dispose`

**接口描述**: 中间环节处置所情

**请求参数**:
```json
{
    "id": "sqcz_uuid_001",               // 所情处置ID
    "sqdjId": "sqdj_uuid_001",           // 所情登记ID
    "handleInfo": "处置情况说明",          // 处置情况
    "attUrl": [                           // 附件URL列表
        "http://example.com/file1.jpg"
    ],
    "glywList": [                         // 关联业务列表（管控岗使用）
        {
            "businessType": "001",        // 业务类型
            "businessId": "business001",  // 业务ID
            "businessName": "相关业务名称" // 业务名称
        }
    ]
}
```

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 3.5 所领导审批

**接口地址**: `POST /approve`

**接口描述**: 所领导审批所情处置结果

**请求参数**:
```json
{
    "id": "sqcz_uuid_001",               // 所情处置ID
    "sqdjId": "sqdj_uuid_001",           // 所情登记ID
    "approveInfoList": [                  // 审批信息列表
        {
            "sqczId": "sqcz_uuid_002",    // 被审批的处置ID
            "approvalResult": "1",        // 审批结果（0:不通过, 1:通过）
            "approvalOpinion": "处置得当"  // 审批意见
        }
    ]
}
```

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

### 3.6 查询所情详情

**接口地址**: `GET /get/{id}`

**接口描述**: 根据ID查询所情详细信息

**路径参数**:
- `id`: 所情登记ID

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": "sqdj_uuid_001",
        "eventCode": "SQ202401010001",
        "eventLevel": "3",
        "eventName": "打架斗殴",
        "status": "1",
        "areaName": "监室A区",
        "eventStartTime": "2024-01-01 10:00:00",
        "eventDetails": "监室内发生打架事件",
        "jgryList": [                     // 监管人员列表
            {
                "personnelName": "李四",
                "roomName": "101监室"
            }
        ],
        "jdList": [                       // 进度列表
            {
                "postName": "巡控岗",
                "handleStatus": "1",
                "handleStatusName": "已办结"
            }
        ],
        "trajectoryList": [               // 业务轨迹
            {
                "handlePostName": "巡控岗",
                "handleUserName": "张三",
                "handleTime": "2024-01-01 10:05:00",
                "handleInfo": "已现场处置"
            }
        ]
    }
}
```

### 3.7 获取当前用户待处理所情

**接口地址**: `GET /getCurrent/{id}`

**接口描述**: 获取当前用户需要处理的所情信息

**路径参数**:
- `id`: 所情登记ID

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "id": "sqcz_uuid_001",
        "sqdjId": "sqdj_uuid_001",
        "handleType": "2",
        "handlePostCode": "03",
        "handlePostName": "管控岗",
        "status": "0",
        "allowList": [                    // 允许办理的业务类型（管控岗）
            {
                "businessType": "001",
                "businessTypeName": "安全检查"
            }
        ],
        "approveInfoList": [              // 审批信息列表（所领导）
            {
                "sqczId": "sqcz_uuid_002",
                "postName": "管控岗"
            }
        ]
    }
}
```

### 3.8 分页查询所情列表

**接口地址**: `GET /page`

**接口描述**: 分页查询所情登记列表

**请求参数**:
```json
{
    "pageNo": 1,                          // 页码
    "pageSize": 10,                       // 页大小
    "eventLevel": "3",                    // 所情等级（可选）
    "status": "1",                        // 状态（可选）
    "eventSrc": "001",                    // 事件源（可选）
    "startTime": "2024-01-01 00:00:00",   // 开始时间（可选）
    "endTime": "2024-01-31 23:59:59"      // 结束时间（可选）
}
```

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "total": 100,
        "list": [
            {
                "id": "sqdj_uuid_001",
                "eventCode": "SQ202401010001",
                "eventName": "打架斗殴",
                "eventLevel": "3",
                "status": "1",
                "areaName": "监室A区",
                "eventStartTime": "2024-01-01 10:00:00",
                "handleUserName": "张三"
            }
        ]
    }
}
```

### 3.9 无需处理

**接口地址**: `POST /noActionRequired/{id}`

**接口描述**: 标记所情无需处理，直接办结

**路径参数**:
- `id`: 所情登记ID

**响应结果**:
```json
{
    "code": 200,
    "message": "操作成功",
    "data": true
}
```

## 4. 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 5. 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| SQGL_001 | 推送对象列表不可为空 | 创建所情时未指定推送对象 |
| SQGL_002 | 所情登记数据不存在 | 指定的所情ID不存在 |
| SQGL_003 | 所领导岗位下未配置用户 | 审批环节找不到所领导用户 |
| SQGL_004 | 当前用户无权限处理此所情 | 用户不在推送对象列表中 |

## 6. 使用示例

### 6.1 完整处置流程示例

```javascript
// 1. 外部系统创建待核实所情
const createResponse = await fetch('/acp/pi/sqgl-sqdj/createWaitVerify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        eventSrc: '003',
        alarmType: '001',
        eventLevel: '2',
        eventStartTime: '2024-01-01 10:00:00',
        eventDetails: '手环异常告警',
        bjgrybm: 'prisoner001',
        orgCode: 'org001',
        orgName: '某某看守所'
    })
});

// 2. 巡控岗核实
const verifyResponse = await fetch('/acp/pi/sqgl-sqdj/verify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        id: 'sqdj_uuid_001',
        saveType: '1',
        handleInfo: '核实情况说明',
        tsdxList: [{
            pushPostCode: '03',
            pushUserSfzh: '110101199001011234',
            pushUserName: '张三'
        }]
    })
});

// 3. 管控岗处置
const disposeResponse = await fetch('/acp/pi/sqgl-sqdj/dispose', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        id: 'sqcz_uuid_001',
        sqdjId: 'sqdj_uuid_001',
        handleInfo: '处置情况说明'
    })
});

// 4. 所领导审批
const approveResponse = await fetch('/acp/pi/sqgl-sqdj/approve', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        id: 'sqcz_uuid_002',
        sqdjId: 'sqdj_uuid_001',
        approveInfoList: [{
            sqczId: 'sqcz_uuid_001',
            approvalResult: '1',
            approvalOpinion: '处置得当'
        }]
    })
});
```

---

**文档版本**: 1.0  
**最后更新**: 2024年  
**维护人员**: 系统开发团队
