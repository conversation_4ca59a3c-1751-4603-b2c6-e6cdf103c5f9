# 所情管理系统数据库设计文档

## 1. 数据库概述

所情管理系统采用关系型数据库设计，主要包含所情登记、所情处置、推送对象、关联人员等核心表，以及相关的配置表和字典表。

## 2. 表结构设计

### 2.1 所情登记表 (acp_pi_sqgl_sqdj)

**表说明**: 存储所情登记的基本信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | | 主键，UUID |
| event_code | VARCHAR | 50 | NULL | | 所情编号，自动生成 |
| event_level | VARCHAR | 10 | NULL | | 所情等级（1-4级） |
| area_id | VARCHAR | 32 | NULL | | 所情地点ID |
| area_name | VARCHAR | 100 | NULL | | 所情地点名称 |
| happen_time | DATETIME | | NULL | | 发生时间 |
| event_template_id | VARCHAR | 32 | NULL | | 所情模板ID |
| event_name | VARCHAR | 200 | NULL | | 所情名称 |
| event_type | VARCHAR | 10 | NULL | | 所情类型 |
| event_start_time | DATETIME | | NULL | | 所情开始时间 |
| event_end_time | DATETIME | | NULL | | 所情结束时间 |
| event_details | TEXT | | NULL | | 所情详情 |
| push_object | TEXT | | NULL | | 推送对象，存JSON |
| status | VARCHAR | 10 | NULL | | 状态（0:待核实,1:待处置,2:待审批,3:已办结） |
| event_src | VARCHAR | 10 | NULL | | 所情来源 |
| handle_user_name | VARCHAR | 50 | NULL | | 巡控岗处置人名称 |
| alarm_event_id | VARCHAR | 100 | NULL | | 设备报警事件订阅ID |
| org_code | VARCHAR | 20 | NULL | | 机构代码 |
| org_name | VARCHAR | 100 | NULL | | 机构名称 |
| add_time | DATETIME | | NULL | CURRENT_TIMESTAMP | 创建时间 |
| add_user | VARCHAR | 32 | NULL | | 创建人 |
| update_time | DATETIME | | NULL | | 更新时间 |
| update_user | VARCHAR | 32 | NULL | | 更新人 |
| deleted | TINYINT | 1 | NULL | 0 | 是否删除（0:否,1:是） |

**索引设计**:
```sql
-- 主键索引
PRIMARY KEY (id)

-- 普通索引
INDEX idx_event_code (event_code)
INDEX idx_status (status)
INDEX idx_event_src (event_src)
INDEX idx_org_code (org_code)
INDEX idx_happen_time (happen_time)
INDEX idx_add_time (add_time)

-- 复合索引
INDEX idx_org_status (org_code, status)
INDEX idx_src_level (event_src, event_level)
```

### 2.2 所情处置表 (acp_pi_sqgl_sqcz)

**表说明**: 存储所情处置记录

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | | 主键，UUID |
| sqdj_id | VARCHAR | 32 | NULL | | 所情登记ID |
| handle_user_sfzh | VARCHAR | 18 | NULL | | 处置人身份证号 |
| handle_user_name | VARCHAR | 50 | NULL | | 处置人名称 |
| handle_time | DATETIME | | NULL | | 处置时间 |
| handle_post_name | VARCHAR | 50 | NULL | | 处置岗位名称 |
| handle_post_code | VARCHAR | 10 | NULL | | 处置岗位编码 |
| hanle_generic_plan | TEXT | | NULL | | 处置预案 |
| handle_info | TEXT | | NULL | | 处置情况 |
| handle_type | VARCHAR | 10 | NULL | | 处置类型（1:巡控,2:中间环节,3:所领导审批） |
| att_url | TEXT | | NULL | | 附件地址 |
| status | VARCHAR | 10 | NULL | | 处置状态（0:未办结,1:已办结,2:不通过,3:通过） |
| approve_info | TEXT | | NULL | | 审批情况，存储审批结果JSON |
| is_timeout | SMALLINT | | NULL | 0 | 是否超时（0:否,1:是） |
| org_code | VARCHAR | 20 | NULL | | 机构代码 |
| org_name | VARCHAR | 100 | NULL | | 机构名称 |
| add_time | DATETIME | | NULL | CURRENT_TIMESTAMP | 创建时间 |
| add_user | VARCHAR | 32 | NULL | | 创建人 |
| update_time | DATETIME | | NULL | | 更新时间 |
| update_user | VARCHAR | 32 | NULL | | 更新人 |
| deleted | TINYINT | 1 | NULL | 0 | 是否删除 |

**索引设计**:
```sql
-- 主键索引
PRIMARY KEY (id)

-- 外键索引
INDEX idx_sqdj_id (sqdj_id)

-- 普通索引
INDEX idx_handle_type (handle_type)
INDEX idx_status (status)
INDEX idx_handle_post_code (handle_post_code)
INDEX idx_handle_user_sfzh (handle_user_sfzh)
INDEX idx_org_code (org_code)

-- 复合索引
INDEX idx_sqdj_type_status (sqdj_id, handle_type, status)
INDEX idx_org_post_status (org_code, handle_post_code, status)
```

### 2.3 推送对象表 (acp_pi_sqgl_sqdjtsdx)

**表说明**: 存储所情推送对象信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | | 主键，UUID |
| sqdj_id | VARCHAR | 32 | NULL | | 所情登记ID |
| sqcz_id | VARCHAR | 32 | NULL | | 所情处置ID |
| push_user_sfzh | VARCHAR | 18 | NULL | | 推送用户身份证号 |
| push_user_name | VARCHAR | 50 | NULL | | 推送用户名称 |
| push_time | DATETIME | | NULL | | 推送时间 |
| push_post_name | VARCHAR | 50 | NULL | | 推送岗位名称 |
| push_post_code | VARCHAR | 10 | NULL | | 推送岗位编码 |
| org_code | VARCHAR | 20 | NULL | | 机构代码 |
| org_name | VARCHAR | 100 | NULL | | 机构名称 |
| add_time | DATETIME | | NULL | CURRENT_TIMESTAMP | 创建时间 |
| add_user | VARCHAR | 32 | NULL | | 创建人 |
| update_time | DATETIME | | NULL | | 更新时间 |
| update_user | VARCHAR | 32 | NULL | | 更新人 |
| deleted | TINYINT | 1 | NULL | 0 | 是否删除 |

**索引设计**:
```sql
-- 主键索引
PRIMARY KEY (id)

-- 外键索引
INDEX idx_sqdj_id (sqdj_id)
INDEX idx_sqcz_id (sqcz_id)

-- 普通索引
INDEX idx_push_user_sfzh (push_user_sfzh)
INDEX idx_push_post_code (push_post_code)
INDEX idx_org_code (org_code)

-- 复合索引
INDEX idx_sqdj_user (sqdj_id, push_user_sfzh)
INDEX idx_sqcz_post (sqcz_id, push_post_code)
```

### 2.4 关联人员表 (acp_pi_sqgl_sqdj_glry)

**表说明**: 存储所情关联的人员信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | | 主键，UUID |
| sqdj_id | VARCHAR | 32 | NULL | | 所情登记ID |
| personnel_type | VARCHAR | 10 | NULL | | 人员类型（1:监管人员,2:工作人员,3:外来人员,4:报警人员） |
| personnel_id | VARCHAR | 32 | NULL | | 人员ID |
| personnel_name | VARCHAR | 50 | NULL | | 人员姓名 |
| photo_url | VARCHAR | 500 | NULL | | 照片URL |
| room_id | VARCHAR | 32 | NULL | | 房间ID |
| room_name | VARCHAR | 100 | NULL | | 房间名称 |
| org_code | VARCHAR | 20 | NULL | | 机构代码 |
| org_name | VARCHAR | 100 | NULL | | 机构名称 |
| add_time | DATETIME | | NULL | CURRENT_TIMESTAMP | 创建时间 |
| add_user | VARCHAR | 32 | NULL | | 创建人 |
| update_time | DATETIME | | NULL | | 更新时间 |
| update_user | VARCHAR | 32 | NULL | | 更新人 |
| deleted | TINYINT | 1 | NULL | 0 | 是否删除 |

**索引设计**:
```sql
-- 主键索引
PRIMARY KEY (id)

-- 外键索引
INDEX idx_sqdj_id (sqdj_id)

-- 普通索引
INDEX idx_personnel_type (personnel_type)
INDEX idx_personnel_id (personnel_id)
INDEX idx_room_id (room_id)
INDEX idx_org_code (org_code)

-- 复合索引
INDEX idx_sqdj_type (sqdj_id, personnel_type)
```

### 2.5 关联业务表 (acp_pi_sqgl_sqcz_gywl)

**表说明**: 存储所情处置关联的业务信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | | 主键，UUID |
| sqcz_id | VARCHAR | 32 | NULL | | 所情处置ID |
| business_type | VARCHAR | 10 | NULL | | 业务类型 |
| business_id | VARCHAR | 32 | NULL | | 业务ID |
| business_name | VARCHAR | 200 | NULL | | 业务名称 |
| add_time | DATETIME | | NULL | CURRENT_TIMESTAMP | 创建时间 |
| add_user | VARCHAR | 32 | NULL | | 创建人 |
| update_time | DATETIME | | NULL | | 更新时间 |
| update_user | VARCHAR | 32 | NULL | | 更新人 |
| deleted | TINYINT | 1 | NULL | 0 | 是否删除 |

**索引设计**:
```sql
-- 主键索引
PRIMARY KEY (id)

-- 外键索引
INDEX idx_sqcz_id (sqcz_id)

-- 普通索引
INDEX idx_business_type (business_type)
INDEX idx_business_id (business_id)
```

## 3. 配置表设计

### 3.1 模板配置表 (acp_pi_sqgl_mbpz)

**表说明**: 存储所情处置模板配置

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | | 主键，UUID |
| template_name | VARCHAR | 100 | NULL | | 模板名称 |
| event_src | VARCHAR | 10 | NULL | | 事件源 |
| alarm_type | VARCHAR | 10 | NULL | | 告警类型 |
| push_object_settings | TEXT | | NULL | | 推送对象设置，JSON格式 |
| org_code | VARCHAR | 20 | NULL | | 机构代码 |
| is_enabled | SMALLINT | | NULL | 1 | 是否启用（0:否,1:是） |
| add_time | DATETIME | | NULL | CURRENT_TIMESTAMP | 创建时间 |
| add_user | VARCHAR | 32 | NULL | | 创建人 |
| update_time | DATETIME | | NULL | | 更新时间 |
| update_user | VARCHAR | 32 | NULL | | 更新人 |
| deleted | TINYINT | 1 | NULL | 0 | 是否删除 |

### 3.2 报警联动设置表 (acp_pi_sqgl_bjldsz)

**表说明**: 存储报警联动配置

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | | 主键，UUID |
| event_src | VARCHAR | 10 | NULL | | 事件源 |
| is_enabled | SMALLINT | | NULL | 1 | 是否启用 |
| optional_linkage_settings | VARCHAR | 200 | NULL | | 可选联动配置，逗号分隔 |
| prompt_sound | VARCHAR | 10 | NULL | | 提示音 |
| processing_duration | SMALLINT | | NULL | | 处理时效（分钟） |
| other_processing_duration | SMALLINT | | NULL | | 其他岗处理时效（分钟） |
| remark | VARCHAR | 500 | NULL | | 备注 |
| org_code | VARCHAR | 20 | NULL | | 机构代码 |
| add_time | DATETIME | | NULL | CURRENT_TIMESTAMP | 创建时间 |
| add_user | VARCHAR | 32 | NULL | | 创建人 |
| update_time | DATETIME | | NULL | | 更新时间 |
| update_user | VARCHAR | 32 | NULL | | 更新人 |
| deleted | TINYINT | 1 | NULL | 0 | 是否删除 |

### 3.3 告警类型配置表 (acp_pi_sqgl_gjlxpz)

**表说明**: 存储告警类型配置

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | VARCHAR | 32 | NOT NULL | | 主键，UUID |
| event_src | VARCHAR | 10 | NULL | | 事件源 |
| alarm_type | VARCHAR | 10 | NULL | | 告警类型 |
| event_level | VARCHAR | 10 | NULL | | 事件级别 |
| org_code | VARCHAR | 20 | NULL | | 机构代码 |
| is_enabled | SMALLINT | | NULL | 1 | 是否启用 |
| add_time | DATETIME | | NULL | CURRENT_TIMESTAMP | 创建时间 |
| add_user | VARCHAR | 32 | NULL | | 创建人 |
| update_time | DATETIME | | NULL | | 更新时间 |
| update_user | VARCHAR | 32 | NULL | | 更新人 |
| deleted | TINYINT | 1 | NULL | 0 | 是否删除 |

## 4. 数据字典

### 4.1 所情等级 (ZD_SQGL_LEVEL)

| 代码 | 名称 | 说明 |
|------|------|------|
| 1 | 一级 | 最高级别 |
| 2 | 二级 | 高级别 |
| 3 | 三级 | 中级别 |
| 4 | 四级 | 低级别 |

### 4.2 所情来源 (ZD_SQGL_EVENT_SRC)

| 代码 | 名称 | 说明 |
|------|------|------|
| 001 | 所情登记 | 手动登记 |
| 002 | 视频分析 | 视频分析告警 |
| 003 | 手环告警 | 手环设备告警 |
| 006 | 周界告警 | 周界设备告警 |
| 010 | 快鱼音频 | 音频设备告警 |

### 4.3 所情状态 (ZD_SQGL_STATUS)

| 代码 | 名称 | 说明 |
|------|------|------|
| 0 | 待核实 | 等待巡控岗核实 |
| 1 | 待处置 | 等待中间环节处置 |
| 2 | 待审批 | 等待所领导审批 |
| 3 | 已办结 | 流程结束 |

### 4.4 岗位编码 (ZD_POST)

| 代码 | 名称 | 说明 |
|------|------|------|
| 02 | 巡控岗 | 巡视控制岗位 |
| 03 | 管控岗 | 管理控制岗位 |
| 04 | 安全岗 | 安全管理岗位 |
| 06 | 所领导 | 所领导岗位 |

## 5. 数据库创建脚本

```sql
-- 创建所情登记表
CREATE TABLE acp_pi_sqgl_sqdj (
    id VARCHAR(32) NOT NULL COMMENT '主键',
    event_code VARCHAR(50) COMMENT '所情编号',
    event_level VARCHAR(10) COMMENT '所情等级',
    area_id VARCHAR(32) COMMENT '所情地点ID',
    area_name VARCHAR(100) COMMENT '所情地点名称',
    happen_time DATETIME COMMENT '发生时间',
    event_template_id VARCHAR(32) COMMENT '所情模板ID',
    event_name VARCHAR(200) COMMENT '所情名称',
    event_type VARCHAR(10) COMMENT '所情类型',
    event_start_time DATETIME COMMENT '所情开始时间',
    event_end_time DATETIME COMMENT '所情结束时间',
    event_details TEXT COMMENT '所情详情',
    push_object TEXT COMMENT '推送对象',
    status VARCHAR(10) COMMENT '状态',
    event_src VARCHAR(10) COMMENT '所情来源',
    handle_user_name VARCHAR(50) COMMENT '巡控岗处置人名称',
    alarm_event_id VARCHAR(100) COMMENT '设备报警事件订阅ID',
    org_code VARCHAR(20) COMMENT '机构代码',
    org_name VARCHAR(100) COMMENT '机构名称',
    add_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    add_user VARCHAR(32) COMMENT '创建人',
    update_time DATETIME COMMENT '更新时间',
    update_user VARCHAR(32) COMMENT '更新人',
    deleted TINYINT(1) DEFAULT 0 COMMENT '是否删除',
    PRIMARY KEY (id),
    INDEX idx_event_code (event_code),
    INDEX idx_status (status),
    INDEX idx_event_src (event_src),
    INDEX idx_org_code (org_code),
    INDEX idx_happen_time (happen_time),
    INDEX idx_org_status (org_code, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='所情登记表';
```

---

**文档版本**: 1.0  
**最后更新**: 2024年  
**维护人员**: 系统开发团队
