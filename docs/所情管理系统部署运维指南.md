# 所情管理系统部署运维指南

## 1. 系统环境要求

### 1.1 硬件要求

| 组件 | 最低配置 | 推荐配置 |
|------|----------|----------|
| CPU | 4核 | 8核以上 |
| 内存 | 8GB | 16GB以上 |
| 硬盘 | 100GB | 500GB以上 |
| 网络 | 100Mbps | 1Gbps |

### 1.2 软件环境

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| JDK | 1.8+ | Oracle JDK或OpenJDK |
| MySQL | 5.7+ | 数据库服务器 |
| Redis | 5.0+ | 缓存服务器 |
| Nginx | 1.16+ | Web服务器（可选） |
| Tomcat | 9.0+ | 应用服务器 |

## 2. 部署架构

### 2.1 单机部署架构
```
┌─────────────────────────────────────┐
│              单机服务器              │
├─────────────────────────────────────┤
│  Nginx (80/443)                     │
├─────────────────────────────────────┤
│  Spring Boot Application (8080)     │
├─────────────────────────────────────┤
│  MySQL (3306)                       │
├─────────────────────────────────────┤
│  Redis (6379)                       │
└─────────────────────────────────────┘
```

### 2.2 集群部署架构
```
┌─────────────┐    ┌─────────────┐
│   Nginx     │    │   Nginx     │
│  (负载均衡)  │    │  (负载均衡)  │
└─────────────┘    └─────────────┘
       │                  │
┌─────────────┐    ┌─────────────┐
│ App Server1 │    │ App Server2 │
│   (8080)    │    │   (8080)    │
└─────────────┘    └─────────────┘
       │                  │
┌─────────────────────────────────────┐
│          MySQL Cluster              │
│     (主从复制/读写分离)              │
└─────────────────────────────────────┘
       │
┌─────────────────────────────────────┐
│          Redis Cluster              │
│        (哨兵模式/集群模式)           │
└─────────────────────────────────────┘
```

## 3. 部署步骤

### 3.1 数据库部署

#### 3.1.1 MySQL安装配置
```bash
# 安装MySQL
sudo yum install mysql-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 配置MySQL
sudo mysql_secure_installation
```

#### 3.1.2 创建数据库和用户
```sql
-- 创建数据库
CREATE DATABASE rs_acp DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'rs_acp'@'%' IDENTIFIED BY 'your_password';

-- 授权
GRANT ALL PRIVILEGES ON rs_acp.* TO 'rs_acp'@'%';
FLUSH PRIVILEGES;
```

#### 3.1.3 导入数据库结构
```bash
# 导入数据库结构
mysql -u rs_acp -p rs_acp < sql/schema.sql

# 导入初始数据
mysql -u rs_acp -p rs_acp < sql/data.sql
```

### 3.2 Redis部署

#### 3.2.1 Redis安装配置
```bash
# 安装Redis
sudo yum install redis

# 配置Redis
sudo vi /etc/redis.conf

# 关键配置项
bind 0.0.0.0
port 6379
requirepass your_redis_password
maxmemory 2gb
maxmemory-policy allkeys-lru

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

### 3.3 应用部署

#### 3.3.1 构建应用
```bash
# 克隆代码
git clone https://github.com/your-repo/rs-master.git
cd rs-master

# 构建项目
mvn clean package -Dmaven.test.skip=true

# 生成的jar包位置
ls -la target/rs-*.jar
```

#### 3.3.2 配置文件
```yaml
# application-prod.yml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  datasource:
    url: ******************************************************************************************************
    username: rs_acp
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-wait: -1ms
        max-idle: 10
        min-idle: 0

# 所情管理配置
sqgl:
  websocket:
    enabled: true
    max-connections: 1000
  message:
    batch-size: 100
    retry-times: 3
    timeout: 30000
  timeout:
    verify: 30
    dispose: 60
    approve: 120

# 日志配置
logging:
  level:
    com.rs.module.acp.service.pi: INFO
  file:
    name: logs/rs-acp.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

#### 3.3.3 启动脚本
```bash
#!/bin/bash
# start.sh

APP_NAME="rs-acp"
JAR_FILE="target/rs-acp-1.0.0.jar"
PID_FILE="$APP_NAME.pid"

# JVM参数
JVM_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# 启动应用
nohup java $JVM_OPTS -jar $JAR_FILE --spring.profiles.active=prod > logs/startup.log 2>&1 &

# 记录PID
echo $! > $PID_FILE

echo "应用启动中，PID: $(cat $PID_FILE)"
echo "日志文件: logs/startup.log"
```

#### 3.3.4 停止脚本
```bash
#!/bin/bash
# stop.sh

APP_NAME="rs-acp"
PID_FILE="$APP_NAME.pid"

if [ -f $PID_FILE ]; then
    PID=$(cat $PID_FILE)
    echo "正在停止应用，PID: $PID"
    kill -15 $PID
    
    # 等待进程结束
    for i in {1..30}; do
        if ! kill -0 $PID 2>/dev/null; then
            echo "应用已停止"
            rm -f $PID_FILE
            exit 0
        fi
        sleep 1
    done
    
    # 强制杀死进程
    echo "强制停止应用"
    kill -9 $PID
    rm -f $PID_FILE
else
    echo "PID文件不存在，应用可能未运行"
fi
```

### 3.4 Nginx配置

#### 3.4.1 Nginx配置文件
```nginx
# /etc/nginx/conf.d/rs-acp.conf

upstream rs_acp_backend {
    server 127.0.0.1:8080 weight=1 max_fails=3 fail_timeout=30s;
    # 集群模式下添加更多服务器
    # server *************:8080 weight=1 max_fails=3 fail_timeout=30s;
    # server *************:8080 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    
    # 日志配置
    access_log /var/log/nginx/rs-acp-access.log;
    error_log /var/log/nginx/rs-acp-error.log;
    
    # 客户端上传限制
    client_max_body_size 100M;
    
    # 静态资源
    location /static/ {
        root /var/www/rs-acp/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://rs_acp_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }
    
    # API代理
    location / {
        proxy_pass http://rs_acp_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

## 4. 监控配置

### 4.1 应用监控

#### 4.1.1 Spring Boot Actuator配置
```yaml
# application.yml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

#### 4.1.2 健康检查端点
```bash
# 检查应用健康状态
curl http://localhost:8080/actuator/health

# 检查应用信息
curl http://localhost:8080/actuator/info

# 检查指标
curl http://localhost:8080/actuator/metrics
```

### 4.2 数据库监控

#### 4.2.1 MySQL监控脚本
```bash
#!/bin/bash
# mysql_monitor.sh

MYSQL_USER="monitor"
MYSQL_PASS="monitor_password"
MYSQL_HOST="localhost"

# 检查MySQL连接
mysql -u$MYSQL_USER -p$MYSQL_PASS -h$MYSQL_HOST -e "SELECT 1" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "MySQL连接正常"
else
    echo "MySQL连接异常"
    # 发送告警
fi

# 检查慢查询
SLOW_QUERIES=$(mysql -u$MYSQL_USER -p$MYSQL_PASS -h$MYSQL_HOST -e "SHOW GLOBAL STATUS LIKE 'Slow_queries'" | awk 'NR==2{print $2}')
echo "慢查询数量: $SLOW_QUERIES"

# 检查连接数
CONNECTIONS=$(mysql -u$MYSQL_USER -p$MYSQL_PASS -h$MYSQL_HOST -e "SHOW GLOBAL STATUS LIKE 'Threads_connected'" | awk 'NR==2{print $2}')
echo "当前连接数: $CONNECTIONS"
```

### 4.3 Redis监控

#### 4.3.1 Redis监控脚本
```bash
#!/bin/bash
# redis_monitor.sh

REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASS="your_redis_password"

# 检查Redis连接
redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASS ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "Redis连接正常"
else
    echo "Redis连接异常"
    # 发送告警
fi

# 检查内存使用
MEMORY_USAGE=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASS info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
echo "内存使用: $MEMORY_USAGE"

# 检查连接数
CONNECTED_CLIENTS=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASS info clients | grep connected_clients | cut -d: -f2 | tr -d '\r')
echo "连接数: $CONNECTED_CLIENTS"
```

## 5. 日志管理

### 5.1 日志配置

#### 5.1.1 Logback配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="prod">
        <!-- 控制台输出 -->
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <!-- 文件输出 -->
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/rs-acp.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/rs-acp.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <!-- 错误日志 -->
        <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/rs-acp-error.log</file>
            <filter class="ch.qos.logback.classic.filter.LevelFilter">
                <level>ERROR</level>
                <onMatch>ACCEPT</onMatch>
                <onMismatch>DENY</onMismatch>
            </filter>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/rs-acp-error.%d{yyyy-MM-dd}.log</fileNamePattern>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
        
        <!-- 所情管理模块日志 -->
        <logger name="com.rs.module.acp.service.pi" level="DEBUG" additivity="false">
            <appender-ref ref="FILE"/>
        </logger>
    </springProfile>
</configuration>
```

### 5.2 日志分析

#### 5.2.1 日志分析脚本
```bash
#!/bin/bash
# log_analysis.sh

LOG_FILE="logs/rs-acp.log"
DATE=$(date +%Y-%m-%d)

echo "=== $DATE 日志分析报告 ==="

# 错误统计
echo "错误日志统计:"
grep "ERROR" $LOG_FILE | grep $DATE | wc -l

# 所情创建统计
echo "所情创建统计:"
grep "所情登记创建成功" $LOG_FILE | grep $DATE | wc -l

# WebSocket连接统计
echo "WebSocket连接统计:"
grep "WebSocket" $LOG_FILE | grep $DATE | wc -l

# 响应时间分析
echo "慢请求统计 (>5秒):"
grep "耗时过长" $LOG_FILE | grep $DATE | wc -l
```

## 6. 备份策略

### 6.1 数据库备份

#### 6.1.1 MySQL备份脚本
```bash
#!/bin/bash
# mysql_backup.sh

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
MYSQL_USER="backup_user"
MYSQL_PASS="backup_password"
DATABASE="rs_acp"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 全量备份
mysqldump -u$MYSQL_USER -p$MYSQL_PASS --single-transaction --routines --triggers $DATABASE > $BACKUP_DIR/rs_acp_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/rs_acp_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "rs_acp_*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: rs_acp_$DATE.sql.gz"
```

### 6.2 应用备份

#### 6.2.1 应用备份脚本
```bash
#!/bin/bash
# app_backup.sh

BACKUP_DIR="/backup/app"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/opt/rs-acp"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用文件
tar -czf $BACKUP_DIR/rs-acp-app_$DATE.tar.gz -C $APP_DIR .

# 备份配置文件
tar -czf $BACKUP_DIR/rs-acp-config_$DATE.tar.gz /etc/nginx/conf.d/rs-acp.conf

# 删除30天前的备份
find $BACKUP_DIR -name "rs-acp-*.tar.gz" -mtime +30 -delete

echo "应用备份完成"
```

## 7. 故障排查

### 7.1 常见问题

#### 7.1.1 应用启动失败
```bash
# 检查端口占用
netstat -tlnp | grep 8080

# 检查Java进程
ps aux | grep java

# 查看启动日志
tail -f logs/startup.log

# 检查配置文件
java -jar rs-acp.jar --spring.config.location=application-prod.yml --debug
```

#### 7.1.2 数据库连接问题
```bash
# 测试数据库连接
mysql -u rs_acp -p -h localhost rs_acp

# 检查数据库状态
systemctl status mysqld

# 查看数据库错误日志
tail -f /var/log/mysqld.log
```

#### 7.1.3 Redis连接问题
```bash
# 测试Redis连接
redis-cli -h localhost -p 6379 -a your_password ping

# 检查Redis状态
systemctl status redis

# 查看Redis日志
tail -f /var/log/redis/redis.log
```

### 7.2 性能调优

#### 7.2.1 JVM调优参数
```bash
# 生产环境JVM参数
-Xms4g -Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=logs/
-XX:+PrintGCDetails
-XX:+PrintGCTimeStamps
-Xloggc:logs/gc.log
```

#### 7.2.2 数据库调优
```sql
-- MySQL配置优化
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
max_connections = 500
```

---

**文档版本**: 1.0  
**最后更新**: 2024年  
**维护人员**: 系统开发团队
