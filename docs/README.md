# 所情管理系统文档中心

## 📋 文档概述

本文档中心包含了所情管理系统的完整技术文档，涵盖系统分析、API接口、数据库设计、部署运维等各个方面。

## 📚 文档目录

### 1. [所情管理系统分析文档](./所情管理系统分析文档.md)
**文档类型**: 系统分析  
**适用人员**: 开发人员、架构师、产品经理  
**内容概要**:
- 系统概述和核心概念
- 实体结构设计
- 事件源分类和触发逻辑
- 三级处置流程详解
- 核心业务方法分析
- 消息推送机制
- 配置管理
- 技术实现细节
- 性能优化策略
- 监控和日志

### 2. [所情管理系统API接口文档](./所情管理系统API接口文档.md)
**文档类型**: 接口文档  
**适用人员**: 前端开发人员、接口调用方、测试人员  
**内容概要**:
- 接口基础信息
- 核心接口详细说明
- 请求参数和响应格式
- 状态码和错误码说明
- 完整使用示例
- 接口调用流程

**主要接口**:
- `POST /create` - 创建所情登记
- `POST /createWaitVerify` - 创建待核实所情
- `POST /verify` - 巡控岗核实
- `POST /dispose` - 中间环节处置
- `POST /approve` - 所领导审批
- `GET /get/{id}` - 查询所情详情
- `GET /getCurrent/{id}` - 获取当前用户待处理所情
- `GET /page` - 分页查询所情列表

### 3. [所情管理系统数据库设计文档](./所情管理系统数据库设计文档.md)
**文档类型**: 数据库设计  
**适用人员**: 数据库管理员、开发人员、架构师  
**内容概要**:
- 数据库概述
- 核心表结构设计
- 配置表设计
- 数据字典定义
- 索引设计策略
- 数据库创建脚本

**核心表结构**:
- `acp_pi_sqgl_sqdj` - 所情登记表
- `acp_pi_sqgl_sqcz` - 所情处置表
- `acp_pi_sqgl_sqdjtsdx` - 推送对象表
- `acp_pi_sqgl_sqdj_glry` - 关联人员表
- `acp_pi_sqgl_sqcz_gywl` - 关联业务表

### 4. [所情管理系统部署运维指南](./所情管理系统部署运维指南.md)
**文档类型**: 部署运维  
**适用人员**: 运维人员、系统管理员、DevOps工程师  
**内容概要**:
- 系统环境要求
- 部署架构设计
- 详细部署步骤
- 监控配置
- 日志管理
- 备份策略
- 故障排查指南
- 性能调优建议

## 🏗️ 系统架构概览

```mermaid
graph TB
    A[外部告警系统] --> B[所情管理系统]
    C[手动录入] --> B
    B --> D[巡控岗核实]
    D --> E{核实结果}
    E -->|直接办结| F[已办结]
    E -->|需要处置| G[中间环节处置]
    G --> H[所领导审批]
    H --> I{审批结果}
    I -->|通过| F
    I -->|不通过| G
    
    B --> J[WebSocket推送]
    B --> K[待办消息]
    B --> L[业务轨迹]
```

## 🔄 业务流程概览

### 事件触发源
- **手动登记** (001): 巡控岗人员手动创建
- **视频分析** (002): 视频分析系统自动触发
- **手环告警** (003): 手环设备异常告警
- **周界告警** (006): 周界设备入侵告警
- **快鱼音频** (010): 音频设备异常告警

### 处置流程
1. **待核实** (状态0): 巡控岗核实事件真实性
2. **待处置** (状态1): 中间环节并行处置
3. **待审批** (状态2): 所领导审批处置结果
4. **已办结** (状态3): 流程结束

### 岗位角色
- **巡控岗** (02): 首次核实环节
- **管控岗** (03): 中间处置环节
- **安全岗** (04): 中间处置环节
- **所领导** (06): 最终审批环节

## 🛠️ 技术栈

### 后端技术
- **框架**: Spring Boot 2.x
- **数据库**: MySQL 5.7+
- **缓存**: Redis 5.0+
- **ORM**: MyBatis Plus
- **消息**: WebSocket
- **工具**: Hutool, FastJSON

### 前端技术
- **框架**: Vue.js
- **UI组件**: Element UI
- **通信**: Axios, WebSocket

### 部署技术
- **容器**: Docker (可选)
- **代理**: Nginx
- **监控**: Spring Boot Actuator
- **日志**: Logback

## 📊 关键指标

### 性能指标
- **响应时间**: API接口平均响应时间 < 500ms
- **并发处理**: 支持1000+并发用户
- **数据处理**: 单日处理所情事件10000+条

### 可用性指标
- **系统可用性**: 99.9%
- **数据一致性**: 强一致性保证
- **故障恢复**: RTO < 30分钟, RPO < 5分钟

## 🔧 快速开始

### 1. 环境准备
```bash
# 安装Java 8+
java -version

# 安装MySQL 5.7+
mysql --version

# 安装Redis 5.0+
redis-server --version
```

### 2. 数据库初始化
```bash
# 创建数据库
mysql -u root -p < sql/create_database.sql

# 导入表结构
mysql -u rs_acp -p rs_acp < sql/schema.sql

# 导入初始数据
mysql -u rs_acp -p rs_acp < sql/data.sql
```

### 3. 应用启动
```bash
# 构建项目
mvn clean package -Dmaven.test.skip=true

# 启动应用
java -jar target/rs-acp-1.0.0.jar --spring.profiles.active=prod
```

### 4. 验证部署
```bash
# 检查应用健康状态
curl http://localhost:8080/actuator/health

# 测试API接口
curl -X GET http://localhost:8080/acp/pi/sqgl-sqdj/page
```

## 📞 技术支持

### 开发团队联系方式
- **项目负责人**: [负责人姓名] - [邮箱地址]
- **技术架构师**: [架构师姓名] - [邮箱地址]
- **运维负责人**: [运维负责人] - [邮箱地址]

### 问题反馈
- **Bug报告**: 请通过项目Issue系统提交
- **功能建议**: 请通过需求管理系统提交
- **紧急问题**: 请联系值班人员

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✅ 完成所情管理核心功能
- ✅ 实现三级处置流程
- ✅ 支持多种事件源触发
- ✅ 完成WebSocket实时推送
- ✅ 完成待办消息推送

### 计划功能
- 🔄 移动端支持
- 🔄 报表统计功能
- 🔄 数据分析功能
- 🔄 AI智能预警

## 📄 许可证

本项目采用 [MIT License](../LICENSE) 许可证。

## 🤝 贡献指南

欢迎贡献代码和文档！请遵循以下步骤：

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📖 相关资源

- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [MyBatis Plus官方文档](https://baomidou.com/)
- [Vue.js官方文档](https://vuejs.org/)
- [Element UI官方文档](https://element.eleme.io/)

---

**文档维护**: 系统开发团队  
**最后更新**: 2024年  
**文档版本**: v1.0.0
