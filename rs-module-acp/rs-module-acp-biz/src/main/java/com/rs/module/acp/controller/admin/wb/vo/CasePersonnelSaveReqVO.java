package com.rs.module.acp.controller.admin.wb.vo;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.rs.framework.mybatis.entity.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import javax.validation.constraints.*;


@ApiModel(description = "管理后台 - 实战平台-窗口业务-办案人员新增/修改 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class CasePersonnelSaveReqVO extends BaseVO{
    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    @ExcelProperty("主键")
    @ApiModelProperty("主键")
    private String id;

    @ExcelProperty("警号")
    @ApiModelProperty("警号")
    private String jh;

    @ExcelProperty("姓名")
    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名不能为空")
    private String xm;

    @ExcelProperty("证件类型")
    @ApiModelProperty("证件类型")
    private String zjlx;

    @ExcelProperty("证件号码")
    @ApiModelProperty("证件号码")
    private String zjhm;

    @ExcelProperty("办案单位代码")
    @ApiModelProperty("办案单位代码")
    private String badwdm;

    @ExcelProperty("办案单位名称")
    @ApiModelProperty("办案单位名称")
    private String badwmc;

    @ExcelProperty("联系方式")
    @ApiModelProperty("联系方式")
    private String lxfs;

    @ExcelProperty("性别")
    @ApiModelProperty("性别")
    private String xb;

    @ExcelProperty("照片存储url")
    @ApiModelProperty("照片存储url")
    private String zpUrl;

    @ExcelProperty("工作证件url")
    @ApiModelProperty("工作证件url")
    private String gzzjUrl;

    @ExcelProperty("提解机关类型(最多两位)")
    @ApiModelProperty("提解机关类型")
    private String orgType;
}
