package com.rs.module.acp.controller.admin.wb;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.rs.module.acp.listener.admin.area.CasePersonnelImportListener;
import com.rs.module.acp.service.wb.DefaultCasePersonneService;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.*;
import java.net.URLEncoder;
import java.util.*;
import com.mzt.bsp.logapi.common.LogOperateType;
import com.mzt.bsp.logapi.starter.annotation.LogRecordAnnotation;

import com.rs.framework.common.pojo.PageResult;
import com.rs.framework.common.pojo.CommonResult;
import com.rs.framework.common.util.object.BeanUtils;

import static com.rs.framework.common.pojo.CommonResult.error;
import static com.rs.framework.common.pojo.CommonResult.success;

import com.rs.module.acp.controller.admin.wb.vo.*;
import com.rs.module.acp.entity.wb.CasePersonnelDO;
import com.rs.module.acp.service.wb.CasePersonnelService;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = "实战平台-窗口业务-办案人员")
@RestController
@RequestMapping("/acp/wb/casePersonnel")
@Validated
public class CasePersonnelController {

    @Resource
    private CasePersonnelService casePersonnelService;

    @PostMapping("/create")
    @ApiOperation(value = "实战平台-窗口业务-创建办案人员")
    @LogRecordAnnotation(bizModule = "acp:casePersonnel:create", operateType = LogOperateType.CREATE, title = "实战平台-窗口业务-创建办案人员",
            success = "实战平台-窗口业务-创建办案人员成功", fail = "实战平台-窗口业务-创建办案人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#createReqVO}}")
    public CommonResult<String> createCasePersonnel(@Valid @RequestBody CasePersonnelSaveReqVO createReqVO) {
        return success(casePersonnelService.createCasePersonnel(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "实战平台-窗口业务-更新办案人员")
    @LogRecordAnnotation(bizModule = "acp:casePersonnel:update", operateType = LogOperateType.UPDATE, title = "实战平台-窗口业务-创建办案人员",
            success = "实战平台-窗口业务-更新办案人员成功", fail = "实战平台-窗口业务-更新办案人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{TO_JSON{#updateReqVO}}")
    public CommonResult<Boolean> updateCasePersonnel(@Valid @RequestBody CasePersonnelSaveReqVO updateReqVO) {
        casePersonnelService.updateCasePersonnel(updateReqVO);
        return success(true);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "实战平台-窗口业务-删除办案人员")
    @ApiImplicitParam(name = "ids", value = "业务ID")
    @LogRecordAnnotation(bizModule = "acp:casePersonnel:delete", operateType = LogOperateType.DELETE, title = "实战平台-窗口业务-删除办案人员",
            success = "实战平台-窗口业务-删除办案人员成功", fail = "实战平台-窗口业务-删除办案人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#ids}}")
    public CommonResult<Boolean> deleteCasePersonnel(@RequestParam("ids") String ids) {
        String[] strings = ids.split(",");
        for (String id : strings) {
           casePersonnelService.deleteCasePersonnel(id);
        }
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation(value = "实战平台-窗口业务-获得办案人员")
    @ApiImplicitParam(name = "id", value = "编号")
    @LogRecordAnnotation(bizModule = "acp:casePersonnel:get", operateType = LogOperateType.QUERY, title = "实战平台-窗口业务-获得办案人员",
            success = "实战平台-窗口业务-获得办案人员成功", fail = "实战平台-窗口业务-获得办案人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<CasePersonnelRespVO> getCasePersonnel(@RequestParam("id") String id) {
        if(ObjectUtil.isEmpty(id)){
            return error("业务ID不可为空！");
        }
        return success(casePersonnelService.getCasePersonnelById(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得实战平台-窗口业务-办案人员分页")
    public CommonResult<PageResult<CasePersonnelRespVO>> getCasePersonnelPage(@Valid @RequestBody CasePersonnelPageReqVO pageReqVO) {
        return success(casePersonnelService.getCasePersonnelPage(pageReqVO));
    }

    @PostMapping("/list")
    @ApiOperation(value = "获得实战平台-窗口业务-办案人员列表")
    public CommonResult<List<CasePersonnelRespVO>> getCasePersonnelList(@Valid @RequestBody CasePersonnelListReqVO listReqVO) {
        return success(casePersonnelService.getCasePersonnelList(listReqVO));
    }

    @GetMapping("/getDefaultCasePersonnelList")
    @ApiOperation(value = "实战平台-窗口业务-根据被监管人员编号获得默认办案人员")
    @ApiImplicitParam(name = "jgrybm", value = "被监管人员编码")
    @LogRecordAnnotation(bizModule = "acp:casePersonnel:getDefaultCasePersonnelList", operateType = LogOperateType.QUERY,
            title = "实战平台-窗口业务-根据被监管人员编号获得默认办案人员",
            success = "实战平台-窗口业务-根据被监管人员编号获得默认办案人员成功",
            fail = "实战平台-窗口业务-根据被监管人员编号获得默认办案人员失败，错误信息：{{#_ret[msg]}}", extraInfo = "{{#id}}")
    public CommonResult<List<CasePersonnelRespVO>> getDefaultCasePersonnelList(@RequestParam("jgrybm") String jgrybm) {
        if(ObjectUtil.isEmpty(jgrybm)){
            return error("被监管人员编码不可为空！");
        }
        return success(casePersonnelService.getDefaultCasePersonnelList(jgrybm));
    }
    @ApiOperation("办案人员导入")
    @PostMapping("/importCasePersonnelData")
    public CommonResult<String> importCasePersonnelData(@RequestParam("file") MultipartFile file) throws Exception {
        List<CasePersonnelSaveReqVO> casePersonnelList = new ArrayList<>();
        CasePersonnelImportListener listener = new CasePersonnelImportListener(casePersonnelList);

        EasyExcel.read(file.getInputStream(), CasePersonnelSaveReqVO.class, listener)
                .sheet(0)
                .headRowNumber(1)
                .doRead();

        // 处理导入的数据
        casePersonnelService.dealCasePersonnelData(casePersonnelList);

        return CommonResult.success("导入办案人员数据成功");
    }

    @ApiOperation("办案人员导入模板下载")
    @RequestMapping(value = "exportModel", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportModel(HttpServletResponse response) {
        try {
            // 这里我们直接使用EasyExcel生成模板，而不是从资源文件中读取
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("办案人员导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            // EasyExcel 写入数据到输出流
            EasyExcel.write(response.getOutputStream(), CasePersonnelSaveReqVO.class).sheet("模板").doWrite(new ArrayList<>());
            // 确保所有数据都已写入并刷新输出流
            response.getOutputStream().flush();

        } catch (Exception e) {
            // log.error("办案人员导入模板下载失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

}
