package com.rs.module.base.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bsp.common.util.CollectionUtil;
import com.bsp.common.util.StringUtil;
import com.bsp.sdk.cons.SDKConstants;
import com.bsp.sdk.msg.SendMessageUtil;
import com.bsp.sdk.msg.model.ReceiveUser;
import com.bsp.security.model.SessionUser;
import com.bsp.security.util.SessionUserUtil;
import com.rs.adapter.bsp.api.UserApi;
import com.rs.adapter.bsp.api.dto.UserRespDTO;
import com.rs.framework.common.util.http.HttpUtils;
import com.rs.framework.common.util.spring.SpringUtils;
import com.rs.module.base.controller.admin.pm.vo.PrisonRoomWarderRespVO;
import com.rs.module.base.controller.admin.pm.vo.PrisonerVwRespVO;
import com.rs.module.base.entity.sys.MsgAddVO;
import com.rs.module.base.entity.sys.MsgTemplateDO;
import com.rs.module.base.enums.MsgTemplatePoliceEnum;
import com.rs.module.base.enums.MsgTemplateStatusEnum;
import com.rs.module.base.enums.MsgTemplateTypeEnum;
import com.rs.module.base.enums.PrisonerQueryRyztEnum;
import com.rs.module.base.service.pm.PrisonerService;
import com.rs.module.base.service.sys.MsgTemplateService;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MsgUtil implements ApplicationContextAware {
    private static UserApi userApi;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        userApi = context.getBean(UserApi.class);
    }

    /**
     * 发送消息
     *
     * @param [vo]
     * @return void
     * <AUTHOR>
     * @date 2025/5/30 11:25
     */
    public static void sendMsg(MsgAddVO vo) {
        MsgTemplateService templateService = SpringUtils.getBean(MsgTemplateService.class);
        PrisonerService prisonerService = SpringUtils.getBean(PrisonerService.class);

        List<MsgTemplateDO> templates = templateService.list(new LambdaQueryWrapper<MsgTemplateDO>().eq(MsgTemplateDO::getModuleCode, vo.getModuleCode()));
        List<MsgTemplateDO> rstList = new ArrayList<>();
        //优先级  单位最高  整个单位都发送
        if (StrUtil.isNotBlank(vo.getOrgCode())) {
            rstList = templates.stream().filter(t -> Objects.equals(vo.getOrgCode(), t.getOrgCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(rstList)) {
            rstList = templates.stream().filter(t -> StrUtil.isBlank(t.getOrgCode())).collect(Collectors.toList());
        }
        MsgTemplateDO template = rstList.size() > 0 ? rstList.get(0) : null;
        if (Objects.isNull(template)) {
            throw new RuntimeException(StrUtil.format("不存在编码为{}的消息模板", vo.getModuleCode()));
        }
        if (!Objects.equals(template.getTemplateStatus(), MsgTemplateStatusEnum.PUBLISHED.getValue())) {
            throw new RuntimeException(StrUtil.format("{}模板未发布", vo.getModuleCode()));
        }

        if (!Objects.equals(template.getIsPolice(), 1) && StrUtil.isBlank(template.getPostId()) && (StrUtil.isBlank(vo.getJgrybm()))) {
            // 不勾选角色或不勾选主协管民警，不推送
            throw new RuntimeException(StrUtil.format("不勾选角色或主协管民警，不推送", vo.getModuleCode()));
        }

        //消息处理页面地址
        String url = vo.getUrl();
        //来源机构代码
        String fOrgCode = StrUtil.isNotBlank(vo.getToOrgCode()) ? vo.getToOrgCode() : template.getOrgCode();
        String pcid = vo.getPcid();            //批次Id
        String ywbh = vo.getBusinessId();                            //业务编号

        String systemMark = null;
        try {
            if (StrUtil.isNotBlank(vo.getSystemMark())) {
                //从请求头获取
                vo.setSystemMark(HttpUtils.getAppCode());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        systemMark = StrUtil.isNotBlank(vo.getSystemMark()) ? vo.getSystemMark() : "acp";        //应用标识


        String fXxpt = StrUtil.isBlank(vo.getSource()) ? "pc" : vo.getSource();//来源消息平台(pc/app)
        String fApp = systemMark;
        String actInstId = null;
        SessionUser sessionUser = getSessionUser();
        String fUser = null, fUserName = "系统";
        String fOrgName = vo.getOrgName();                //来源机构名称
        if (sessionUser != null) {
            fUser = sessionUser.getIdCard();
            fUserName = sessionUser.getName();
            fOrgName = sessionUser.getOrgName();
        }

        String title = StrUtil.isBlank(vo.getTitle()) ? template.getMsgTitle() : vo.getTitle();
        //正则匹配 ${key} 格式
        if (vo.getContentData() == null) {
            vo.setContentData(new HashMap<>());
        }

        String content = ReUtil.replaceAll(template.getMsgContent(), "\\$\\{(\\w+)}", m -> vo.getContentData().getOrDefault(m.group(1), "").toString());
        List<ReceiveUser> receiveUserList = new ArrayList<>();

        //判断是否是指定接收用户
        if (vo.isSpecify()) {
            receiveUserList.addAll(vo.getSpecifyReceiveUserList());
        } else {
            //主管，协管发送
            if (MsgTemplatePoliceEnum.YES.getValue().equals(template.getIsPolice())) {
                if (StrUtil.isBlank(vo.getJgrybm())) {
                    throw new RuntimeException(StrUtil.format("人员编码传入有误：{}", vo.getJgrybm()));
                }
                PrisonerVwRespVO vwRespVO = prisonerService.getPrisonerSelectCompomenOne(vo.getJgrybm(), PrisonerQueryRyztEnum.ALL);
                vo.getContentData().put("roomName", vwRespVO.getRoomName());
                vo.getContentData().put("prisonerName", vwRespVO.getXm());
                content = ReUtil.replaceAll(template.getMsgContent(), "\\$\\{(\\w+)}", m -> vo.getContentData().getOrDefault(m.group(1), "").toString());
                //主协管
                List<PrisonRoomWarderRespVO> roomWarderRespVOList = vwRespVO.getZgzjList();
                roomWarderRespVOList.forEach(e -> {
                    receiveUserList.add(new ReceiveUser(e.getPoliceSfzh(), fOrgCode));
                });

            } else if (StringUtil.isNotEmpty(template.getPostId())) { //需该字段配置为岗位字典 ZD_POST 代码值
                List<UserRespDTO> listUser = userApi.getUserByOrgAndPost(fOrgCode, template.getPostId());
                if (CollectionUtil.isNull(listUser)) throw new RuntimeException("未找到岗位对应的用户");
                listUser.forEach(e -> {
                    receiveUserList.add(new ReceiveUser(e.getIdCard(), e.getOrgCode()));
                });
            }
        }

        //TODO 岗位推送待实现

        if (template.getMsgType().equals(MsgTemplateTypeEnum.NOTICE_MSG.getValue()) && CollUtil.isNotEmpty(receiveUserList)) {
            SendMessageUtil.sendAlertMsg(title, content, url, fApp, fUser, fUserName, fOrgCode, fOrgName, actInstId, pcid, fXxpt, ywbh, receiveUserList, systemMark, vo.getMsgType(), vo.getExtendData());
        }

        if (template.getMsgType().equals(MsgTemplateTypeEnum.TO_DO_MSG.getValue()) && CollUtil.isNotEmpty(receiveUserList)) {
            //SendMessageUtil.sendTodoMsg(title, content, url, fApp, fUser, fUserName, fOrgCode, fOrgName, fXxpt, ywbh, receiveUserList);
            SendMessageUtil.sendTodoMsg(title, content, url, fApp, fUser, fUserName, fOrgCode, fOrgName, actInstId, pcid, fXxpt, ywbh, receiveUserList, SDKConstants.SYSTEM_MARK);

        }

    }

    private static SessionUser getSessionUser() {
        try {
            return SessionUserUtil.getSessionUser();
        } catch (Exception e) {
            return null;
        }
    }
    //添加执行器
}
