package com.rs.enums;

/**
 * 机构类型枚举
 */
public enum InstitutionTypeEnum {

    /**
     * 看守所
     */
    DETENTION_CENTER("01", "看守所"),

    /**
     * 拘留所
     */
    DETENTION_HOUSE("02", "拘留所"),

    /**
     * 戒毒所
     */
    DRUG_REHABILITATION_CENTER("03", "戒毒所"),

    /**
     * 医疗所
     */
    MEDICAL_CENTER("04", "医疗所"),

    /**
     * 监管支队
     */
    SUPERVISION_BRIGADE("05", "监管支队"),

    /**
     * 监管总队
     */
    SUPERVISION_HEADQUARTERS("06", "监管总队");

    private final String code;
    private final String displayName;

    InstitutionTypeEnum(String code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }

    public String getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * 根据编码获取对应的枚举实例
     *
     * @param code 编码
     * @return 对应的枚举
     * @throws IllegalArgumentException 如果未找到匹配项
     */
    public static InstitutionTypeEnum fromCode(String code) {
        for (InstitutionTypeEnum type : InstitutionTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No constant with code: " + code);
    }

    @Override
    public String toString() {
        return "InstitutionTypeEnum{" +
                "code='" + code + '\'' +
                ", displayName='" + displayName + '\'' +
                '}';
    }
}
