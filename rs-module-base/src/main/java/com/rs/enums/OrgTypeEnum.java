package com.rs.enums;

/**
 * 机构类型枚举
 */
public enum OrgTypeEnum {

    /**
     * 看守所
     */
    DETENTION_CENTER("01", "看守所"),

    /**
     * 拘留所
     */
    DETENTION_HOUSE("02", "拘留所"),

    /**
     * 戒毒所
     */
    DRUG_REHABILITATION_CENTER("03", "戒毒所"),

    /**
     * 医疗所
     */
    MEDICAL_CENTER("04", "医疗所"),

    /**
     * 监管支队
     */
    SUPERVISION_BRIGADE("05", "监管支队"),

    /**
     * 监管总队
     */
    SUPERVISION_HEADQUARTERS("06", "监管总队");

    private final String code;
    private final String displayName;

    OrgTypeEnum(String code, String displayName) {
        this.code = code;
        this.displayName = displayName;
    }

    public String getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * 根据编码获取对应的枚举实例
     *
     * @param code 编码
     * @return 对应的枚举
     * @throws IllegalArgumentException 如果未找到匹配项
     */
    public static OrgTypeEnum fromCode(String code) {
        for (OrgTypeEnum type : OrgTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No constant with code: " + code);
    }



    /**
     * 是否为看守所
     *
     * @return true 如果是看守所
     */
    public boolean isDetentionCenter() {
        return this == DETENTION_CENTER;
    }

    /**
     * 是否为拘留所
     *
     * @return true 如果是拘留所
     */
    public boolean isDetentionHouse() {
        return this == DETENTION_HOUSE;
    }

    /**
     * 是否为戒毒所
     *
     * @return true 如果是戒毒所
     */
    public boolean isDrugRehabilitationCenter() {
        return this == DRUG_REHABILITATION_CENTER;
    }

    /**
     * 是否为医疗所
     *
     * @return true 如果是医疗所
     */
    public boolean isMedicalCenter() {
        return this == MEDICAL_CENTER;
    }

    /**
     * 是否为监管支队
     *
     * @return true 如果是监管支队
     */
    public boolean isSupervisionBrigade() {
        return this == SUPERVISION_BRIGADE;
    }

    /**
     * 是否为监管总队
     *
     * @return true 如果是监管总队
     */
    public boolean isSupervisionHeadquarters() {
        return this == SUPERVISION_HEADQUARTERS;
    }

    /**
     * 是否为监管机构（监管支队或监管总队）
     *
     * @return true 如果是监管机构
     */
    public boolean isSupervisionOrganization() {
        return this == SUPERVISION_BRIGADE || this == SUPERVISION_HEADQUARTERS;
    }

    /**
     * 是否为拘押机构（看守所或拘留所）
     *
     * @return true 如果是拘押机构
     */
    public boolean isDetentionOrganization() {
        return this == DETENTION_CENTER || this == DETENTION_HOUSE;
    }

    /**
     * 是否为特殊机构（戒毒所或医疗所）
     *
     * @return true 如果是特殊机构
     */
    public boolean isSpecialOrganization() {
        return this == DRUG_REHABILITATION_CENTER || this == MEDICAL_CENTER;
    }

    /**
     * 是否为基层执行机构（看守所、拘留所、戒毒所、医疗所）
     *
     * @return true 如果是基层执行机构
     */
    public boolean isExecutionOrganization() {
        return this == DETENTION_CENTER || this == DETENTION_HOUSE
            || this == DRUG_REHABILITATION_CENTER || this == MEDICAL_CENTER;
    }

    @Override
    public String toString() {
        return "InstitutionTypeEnum{" +
                "code='" + code + '\'' +
                ", displayName='" + displayName + '\'' +
                '}';
    }
}
